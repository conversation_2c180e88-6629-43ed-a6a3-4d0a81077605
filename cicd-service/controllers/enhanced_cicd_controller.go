package controllers

import (
	"net/http"
	"strconv"

	"github.com/devops-microservices/cicd-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedCICDController 增强的CI/CD控制器
type EnhancedCICDController struct {
	db          *gorm.DB
	log         *logrus.Logger
	cicdService *services.EnhancedCICDService
}

// NewEnhancedCICDController 创建增强的CI/CD控制器
func NewEnhancedCICDController(db *gorm.DB, log *logrus.Logger, cicdService *services.EnhancedCICDService) *EnhancedCICDController {
	return &EnhancedCICDController{
		db:          db,
		log:         log,
		cicdService: cicdService,
	}
}

// GetPipelineMetrics 获取流水线指标
// @Tags CI/CD Enhanced
// @Summary 获取流水线指标
// @Description 获取流水线的详细指标，包括成功率、构建趋势、失败原因等
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围: 1d|7d|30d" default(7d)
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/cicd/metrics [get]
func (c *EnhancedCICDController) GetPipelineMetrics(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "7d")

	metrics, err := c.cicdService.GetPipelineMetrics(ctx.Request.Context(), timeRange)
	if err != nil {
		c.log.WithError(err).Error("获取流水线指标失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取流水线指标失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": metrics,
	})
}

// GetDeploymentStatus 获取部署状态
// @Tags CI/CD Enhanced
// @Summary 获取部署状态
// @Description 获取应用在指定环境的部署状态
// @Accept json
// @Produce json
// @Param app_id path string true "应用ID"
// @Param environment query string false "环境名称" default(production)
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/cicd/apps/{app_id}/deployment/status [get]
func (c *EnhancedCICDController) GetDeploymentStatus(ctx *gin.Context) {
	appIDStr := ctx.Param("app_id")
	appID, err := strconv.ParseUint(appIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的应用ID",
		})
		return
	}

	environment := ctx.DefaultQuery("environment", "production")

	status, err := c.cicdService.GetDeploymentStatus(ctx.Request.Context(), uint(appID), environment)
	if err != nil {
		c.log.WithError(err).Error("获取部署状态失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取部署状态失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": status,
	})
}

// TriggerPipeline 触发流水线
// @Tags CI/CD Enhanced
// @Summary 触发流水线
// @Description 手动触发应用的CI/CD流水线
// @Accept json
// @Produce json
// @Param app_id path string true "应用ID"
// @Param request body TriggerPipelineRequest true "触发参数"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/cicd/apps/{app_id}/pipeline/trigger [post]
func (c *EnhancedCICDController) TriggerPipeline(ctx *gin.Context) {
	appIDStr := ctx.Param("app_id")
	appID, err := strconv.ParseUint(appIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的应用ID",
		})
		return
	}

	var request TriggerPipelineRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	buildJob, err := c.cicdService.TriggerPipeline(ctx.Request.Context(), uint(appID), request.Branch, request.Params)
	if err != nil {
		c.log.WithError(err).Error("触发流水线失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "触发流水线失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": buildJob,
		"message": "流水线已成功触发",
	})
}

// GetBuildTrends 获取构建趋势
// @Tags CI/CD Enhanced
// @Summary 获取构建趋势
// @Description 获取指定时间范围内的构建趋势数据
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围: 1d|7d|30d" default(7d)
// @Param app_id query string false "应用ID筛选"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/cicd/trends [get]
func (c *EnhancedCICDController) GetBuildTrends(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "7d")

	metrics, err := c.cicdService.GetPipelineMetrics(ctx.Request.Context(), timeRange)
	if err != nil {
		c.log.WithError(err).Error("获取构建趋势失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取构建趋势失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"trends":      metrics.BuildTrends,
			"success_rate": metrics.SuccessRate,
			"total_builds": metrics.SuccessfulBuilds + metrics.FailedBuilds,
		},
	})
}

// GetFailureAnalysis 获取失败分析
// @Tags CI/CD Enhanced
// @Summary 获取失败分析
// @Description 获取构建失败的详细分析，包括失败原因统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围: 1d|7d|30d" default(7d)
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/cicd/failure-analysis [get]
func (c *EnhancedCICDController) GetFailureAnalysis(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "7d")

	metrics, err := c.cicdService.GetPipelineMetrics(ctx.Request.Context(), timeRange)
	if err != nil {
		c.log.WithError(err).Error("获取失败分析失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取失败分析失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"failure_reasons": metrics.TopFailureReasons,
			"failed_builds":   metrics.FailedBuilds,
			"success_rate":    metrics.SuccessRate,
		},
	})
}

// GetResourceUsage 获取资源使用情况
// @Tags CI/CD Enhanced
// @Summary 获取资源使用情况
// @Description 获取CI/CD系统的资源使用情况
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/cicd/resources [get]
func (c *EnhancedCICDController) GetResourceUsage(ctx *gin.Context) {
	metrics, err := c.cicdService.GetPipelineMetrics(ctx.Request.Context(), "1d")
	if err != nil {
		c.log.WithError(err).Error("获取资源使用情况失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取资源使用情况失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"resource_usage":   metrics.ResourceUsage,
			"running_pipelines": metrics.RunningPipelines,
			"total_pipelines":   metrics.TotalPipelines,
		},
	})
}

// GetQualityMetrics 获取质量指标
// @Tags CI/CD Enhanced
// @Summary 获取质量指标
// @Description 获取代码质量相关的指标
// @Accept json
// @Produce json
// @Param app_id query string false "应用ID筛选"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/cicd/quality [get]
func (c *EnhancedCICDController) GetQualityMetrics(ctx *gin.Context) {
	metrics, err := c.cicdService.GetPipelineMetrics(ctx.Request.Context(), "7d")
	if err != nil {
		c.log.WithError(err).Error("获取质量指标失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取质量指标失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": metrics.QualityMetrics,
	})
}

// GetPipelineOverview 获取流水线概览
// @Tags CI/CD Enhanced
// @Summary 获取流水线概览
// @Description 获取CI/CD流水线的整体概览信息
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/cicd/overview [get]
func (c *EnhancedCICDController) GetPipelineOverview(ctx *gin.Context) {
	metrics, err := c.cicdService.GetPipelineMetrics(ctx.Request.Context(), "7d")
	if err != nil {
		c.log.WithError(err).Error("获取流水线概览失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取流水线概览失败: " + err.Error(),
		})
		return
	}

	overview := gin.H{
		"summary": gin.H{
			"total_pipelines":    metrics.TotalPipelines,
			"running_pipelines":  metrics.RunningPipelines,
			"successful_builds":  metrics.SuccessfulBuilds,
			"failed_builds":      metrics.FailedBuilds,
			"success_rate":       metrics.SuccessRate,
			"average_build_time": metrics.AverageTime,
		},
		"trends":           metrics.BuildTrends,
		"failure_reasons":  metrics.TopFailureReasons,
		"resource_usage":   metrics.ResourceUsage,
		"quality_metrics":  metrics.QualityMetrics,
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": overview,
	})
}

// TriggerPipelineRequest 触发流水线请求
type TriggerPipelineRequest struct {
	Branch string                 `json:"branch" binding:"required"`
	Params map[string]interface{} `json:"params"`
}
