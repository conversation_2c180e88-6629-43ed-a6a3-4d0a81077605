package controllers

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// BaseController 基础控制器
type BaseController struct {
	db        *gorm.DB
	log       *logrus.Logger
	response  *ResponseHelper
	validator *validator.Validate
}

// NewBaseController 创建基础控制器
func NewBaseController(db *gorm.DB, log *logrus.Logger) *BaseController {
	v := validator.New()

	// 注册自定义验证器
	registerCustomValidators(v)

	return &BaseController{
		db:        db,
		log:       log,
		response:  NewResponseHelper(),
		validator: v,
	}
}

// registerCustomValidators 注册自定义验证器
func registerCustomValidators(v *validator.Validate) {
	// 注册产品代码验证器
	v.RegisterValidation("product_code", validateProductCode)

	// 注册项目代码验证器
	v.RegisterValidation("project_code", validateProjectCode)

	// 注册应用代码验证器
	v.RegisterValidation("app_code", validateAppCode)

	// 注册环境代码验证器
	v.RegisterValidation("env_code", validateEnvCode)

	// 注册区域代码验证器
	v.RegisterValidation("region_code", validateRegionCode)
}

// ParseUintParam 解析URL参数为uint
func (c *BaseController) ParseUintParam(ctx *gin.Context, paramName string) (uint, error) {
	param := ctx.Param(paramName)
	id, err := strconv.ParseUint(param, 10, 32)
	if err != nil {
		return 0, err
	}
	return uint(id), nil
}

// ParseIntQuery 解析查询参数为int
func (c *BaseController) ParseIntQuery(ctx *gin.Context, paramName string, defaultValue int) int {
	param := ctx.Query(paramName)
	if param == "" {
		return defaultValue
	}
	value, err := strconv.Atoi(param)
	if err != nil {
		return defaultValue
	}
	return value
}

// ValidateRequest 验证请求数据
func (c *BaseController) ValidateRequest(ctx *gin.Context, req interface{}) bool {
	if err := ctx.ShouldBindJSON(req); err != nil {
		c.response.BadRequest(ctx, "请求参数格式错误: "+err.Error())
		return false
	}

	if err := c.validator.Struct(req); err != nil {
		// 格式化验证错误信息
		errorMsg := c.formatValidationErrors(err)
		c.response.ValidationError(ctx, errorMsg)
		return false
	}

	return true
}

// ValidateQuery 验证查询参数
func (c *BaseController) ValidateQuery(ctx *gin.Context, req interface{}) bool {
	if err := ctx.ShouldBindQuery(req); err != nil {
		c.response.BadRequest(ctx, "查询参数格式错误: "+err.Error())
		return false
	}

	if err := c.validator.Struct(req); err != nil {
		c.response.ValidationError(ctx, "查询参数验证失败: "+err.Error())
		return false
	}

	return true
}

// GetPaginationParams 获取分页参数
func (c *BaseController) GetPaginationParams(ctx *gin.Context) (page, pageSize int) {
	page = c.ParseIntQuery(ctx, "page", 1)
	pageSize = c.ParseIntQuery(ctx, "page_size", 20)

	// 限制页大小
	if pageSize > 100 {
		pageSize = 100
	}
	if pageSize < 1 {
		pageSize = 20
	}
	if page < 1 {
		page = 1
	}

	return page, pageSize
}

// validateProductCode 验证产品代码格式
func validateProductCode(fl validator.FieldLevel) bool {
	code := fl.Field().String()
	if code == "" {
		return true // 允许空值，由required标签处理
	}
	// 产品代码：字母开头，可包含字母、数字、下划线、连字符，长度2-50
	return len(code) >= 2 && len(code) <= 50 && isValidCodeFormat(code)
}

// validateProjectCode 验证项目代码格式
func validateProjectCode(fl validator.FieldLevel) bool {
	code := fl.Field().String()
	if code == "" {
		return true
	}
	// 项目代码：字母开头，可包含字母、数字、下划线、连字符，长度2-100
	return len(code) >= 2 && len(code) <= 100 && isValidCodeFormat(code)
}

// validateAppCode 验证应用代码格式
func validateAppCode(fl validator.FieldLevel) bool {
	code := fl.Field().String()
	if code == "" {
		return true
	}
	// 应用代码：字母开头，可包含字母、数字、下划线、连字符，长度2-250
	return len(code) >= 2 && len(code) <= 250 && isValidCodeFormat(code)
}

// validateEnvCode 验证环境代码格式
func validateEnvCode(fl validator.FieldLevel) bool {
	code := fl.Field().String()
	if code == "" {
		return true
	}
	// 环境代码：字母开头，可包含字母、数字、下划线、连字符，长度2-100
	return len(code) >= 2 && len(code) <= 100 && isValidCodeFormat(code)
}

// validateRegionCode 验证区域代码格式
func validateRegionCode(fl validator.FieldLevel) bool {
	code := fl.Field().String()
	if code == "" {
		return true
	}
	// 区域代码：字母开头，可包含字母、数字、下划线、连字符，长度2-100
	return len(code) >= 2 && len(code) <= 100 && isValidCodeFormat(code)
}

// isValidCodeFormat 检查代码格式是否有效
func isValidCodeFormat(code string) bool {
	if len(code) == 0 {
		return false
	}

	// 必须以字母开头
	if !isLetter(code[0]) {
		return false
	}

	// 检查每个字符
	for _, char := range code {
		if !isLetter(byte(char)) && !isDigit(byte(char)) && char != '_' && char != '-' {
			return false
		}
	}

	return true
}

// isLetter 检查是否为字母
func isLetter(c byte) bool {
	return (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z')
}

// isDigit 检查是否为数字
func isDigit(c byte) bool {
	return c >= '0' && c <= '9'
}

// formatValidationErrors 格式化验证错误信息
func (c *BaseController) formatValidationErrors(err error) string {
	var errors []string

	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		for _, fieldError := range validationErrors {
			fieldName := fieldError.Field()
			tag := fieldError.Tag()

			var message string
			switch tag {
			case "required":
				message = fmt.Sprintf("%s 是必填字段", fieldName)
			case "min":
				message = fmt.Sprintf("%s 长度不能少于 %s 个字符", fieldName, fieldError.Param())
			case "max":
				message = fmt.Sprintf("%s 长度不能超过 %s 个字符", fieldName, fieldError.Param())
			case "email":
				message = fmt.Sprintf("%s 必须是有效的邮箱地址", fieldName)
			case "product_code":
				message = fmt.Sprintf("%s 格式不正确，必须以字母开头，可包含字母、数字、下划线、连字符，长度2-50", fieldName)
			case "project_code":
				message = fmt.Sprintf("%s 格式不正确，必须以字母开头，可包含字母、数字、下划线、连字符，长度2-100", fieldName)
			case "app_code":
				message = fmt.Sprintf("%s 格式不正确，必须以字母开头，可包含字母、数字、下划线、连字符，长度2-250", fieldName)
			case "env_code":
				message = fmt.Sprintf("%s 格式不正确，必须以字母开头，可包含字母、数字、下划线、连字符，长度2-100", fieldName)
			case "region_code":
				message = fmt.Sprintf("%s 格式不正确，必须以字母开头，可包含字母、数字、下划线、连字符，长度2-100", fieldName)
			case "oneof":
				message = fmt.Sprintf("%s 的值必须是以下之一: %s", fieldName, fieldError.Param())
			default:
				message = fmt.Sprintf("%s 验证失败: %s", fieldName, tag)
			}

			errors = append(errors, message)
		}
	} else {
		return err.Error()
	}

	if len(errors) == 0 {
		return "参数验证失败"
	}

	return strings.Join(errors, "; ")
}
