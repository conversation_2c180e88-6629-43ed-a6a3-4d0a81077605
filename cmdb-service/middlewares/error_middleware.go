package middlewares

import (
	"net/http"
	"runtime/debug"

	"github.com/devops-microservices/cmdb-service/models"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// ErrorMiddleware 错误处理中间件
func ErrorMiddleware(log *logrus.Logger) gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			log.WithFields(logrus.Fields{
				"error": err,
				"path":  c.Request.URL.Path,
				"method": c.Request.Method,
				"stack": string(debug.Stack()),
			}).Error("Panic recovered")
		}
		
		// 返回统一的错误响应
		c.JSON(http.StatusInternalServerError, models.DTOResponse{
			Code:    models.CodeInternalError,
			Message: "服务器内部错误",
		})
		c.Abort()
	})
}

// APIError 自定义API错误类型
type APIError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

func (e *APIError) Error() string {
	return e.Message
}

// NewAPIError 创建API错误
func NewAPIError(code int, message string, details ...string) *APIError {
	err := &APIError{
		Code:    code,
		Message: message,
	}
	if len(details) > 0 {
		err.Details = details[0]
	}
	return err
}

// 预定义的错误
var (
	ErrBadRequest     = NewAPIError(models.CodeBadRequest, "请求参数错误")
	ErrUnauthorized   = NewAPIError(models.CodeUnauthorized, "未授权访问")
	ErrForbidden      = NewAPIError(models.CodeForbidden, "禁止访问")
	ErrNotFound       = NewAPIError(models.CodeNotFound, "资源不存在")
	ErrInternalError  = NewAPIError(models.CodeInternalError, "服务器内部错误")
	ErrValidation     = NewAPIError(models.CodeValidationError, "数据验证失败")
)

// HandleError 统一错误处理函数
func HandleError(c *gin.Context, err error, log *logrus.Logger) {
	if apiErr, ok := err.(*APIError); ok {
		// 自定义API错误
		c.JSON(http.StatusOK, models.DTOResponse{
			Code:    apiErr.Code,
			Message: apiErr.Message,
		})
		
		// 记录错误日志
		log.WithFields(logrus.Fields{
			"error_code": apiErr.Code,
			"error_msg":  apiErr.Message,
			"details":    apiErr.Details,
			"path":       c.Request.URL.Path,
			"method":     c.Request.Method,
		}).Error("API error occurred")
		
		return
	}
	
	// 其他错误，统一处理为内部错误
	log.WithFields(logrus.Fields{
		"error":  err.Error(),
		"path":   c.Request.URL.Path,
		"method": c.Request.Method,
	}).Error("Unexpected error occurred")
	
	c.JSON(http.StatusOK, models.DTOResponse{
		Code:    models.CodeInternalError,
		Message: "服务器内部错误",
	})
}

// RequestLogger 请求日志中间件
func RequestLogger(log *logrus.Logger) gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		log.WithFields(logrus.Fields{
			"status_code":  param.StatusCode,
			"latency":      param.Latency,
			"client_ip":    param.ClientIP,
			"method":       param.Method,
			"path":         param.Path,
			"user_agent":   param.Request.UserAgent(),
			"error":        param.ErrorMessage,
		}).Info("API request")
		
		return ""
	})
}

// CORSMiddleware CORS中间件
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RateLimitMiddleware 简单的速率限制中间件
func RateLimitMiddleware() gin.HandlerFunc {
	// 这里可以集成更复杂的速率限制库，如 golang.org/x/time/rate
	return func(c *gin.Context) {
		// 简单实现，实际项目中应该使用更完善的速率限制
		c.Next()
	}
}

// SecurityHeadersMiddleware 安全头中间件
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		c.Header("Content-Security-Policy", "default-src 'self'")
		c.Next()
	}
}
