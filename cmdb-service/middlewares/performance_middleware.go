package middlewares

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	RequestCount    int64         `json:"request_count"`
	AverageLatency  time.Duration `json:"average_latency"`
	MaxLatency      time.Duration `json:"max_latency"`
	MinLatency      time.Duration `json:"min_latency"`
	ErrorCount      int64         `json:"error_count"`
	LastRequestTime time.Time     `json:"last_request_time"`
}

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	metrics map[string]*PerformanceMetrics
	log     *logrus.Logger
}

// NewPerformanceMonitor 创建性能监控器
func NewPerformanceMonitor(log *logrus.Logger) *PerformanceMonitor {
	return &PerformanceMonitor{
		metrics: make(map[string]*PerformanceMetrics),
		log:     log,
	}
}

// PerformanceMiddleware 性能监控中间件
func (pm *PerformanceMonitor) PerformanceMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method

		// 处理请求
		c.Next()

		// 计算延迟
		latency := time.Since(start)
		statusCode := c.Writer.Status()

		// 更新指标
		pm.updateMetrics(method, path, latency, statusCode)

		// 记录性能日志
		pm.logPerformance(c, latency, statusCode)

		// 检查慢查询
		if latency > 5*time.Second {
			pm.log.WithFields(logrus.Fields{
				"method":      method,
				"path":        path,
				"latency_ms":  latency.Milliseconds(),
				"status_code": statusCode,
				"client_ip":   c.ClientIP(),
			}).Warn("Slow request detected")
		}
	}
}

// updateMetrics 更新性能指标
func (pm *PerformanceMonitor) updateMetrics(method, path string, latency time.Duration, statusCode int) {
	key := method + " " + path

	if pm.metrics[key] == nil {
		pm.metrics[key] = &PerformanceMetrics{
			MinLatency: latency,
			MaxLatency: latency,
		}
	}

	metrics := pm.metrics[key]
	metrics.RequestCount++
	metrics.LastRequestTime = time.Now()

	// 更新延迟统计
	if latency > metrics.MaxLatency {
		metrics.MaxLatency = latency
	}
	if latency < metrics.MinLatency {
		metrics.MinLatency = latency
	}

	// 计算平均延迟（简单移动平均）
	metrics.AverageLatency = (metrics.AverageLatency*time.Duration(metrics.RequestCount-1) + latency) / time.Duration(metrics.RequestCount)

	// 统计错误
	if statusCode >= 400 {
		metrics.ErrorCount++
	}
}

// logPerformance 记录性能日志
func (pm *PerformanceMonitor) logPerformance(c *gin.Context, latency time.Duration, statusCode int) {
	fields := logrus.Fields{
		"method":      c.Request.Method,
		"path":        c.Request.URL.Path,
		"status_code": statusCode,
		"latency_ms":  latency.Milliseconds(),
		"client_ip":   c.ClientIP(),
		"user_agent":  c.Request.UserAgent(),
	}

	// 添加请求大小信息
	if c.Request.ContentLength > 0 {
		fields["request_size"] = c.Request.ContentLength
	}

	// 添加响应大小信息
	if size := c.Writer.Size(); size > 0 {
		fields["response_size"] = size
	}

	// 根据状态码选择日志级别
	switch {
	case statusCode >= 500:
		pm.log.WithFields(fields).Error("Server error")
	case statusCode >= 400:
		pm.log.WithFields(fields).Warn("Client error")
	case latency > 2*time.Second:
		pm.log.WithFields(fields).Warn("Slow request")
	default:
		pm.log.WithFields(fields).Info("Request completed")
	}
}

// GetMetrics 获取性能指标
func (pm *PerformanceMonitor) GetMetrics() map[string]*PerformanceMetrics {
	return pm.metrics
}

// GetMetricsForEndpoint 获取特定端点的性能指标
func (pm *PerformanceMonitor) GetMetricsForEndpoint(method, path string) *PerformanceMetrics {
	key := method + " " + path
	return pm.metrics[key]
}

// ResetMetrics 重置性能指标
func (pm *PerformanceMonitor) ResetMetrics() {
	pm.metrics = make(map[string]*PerformanceMetrics)
	pm.log.Info("Performance metrics reset")
}

// HealthCheckMiddleware 健康检查中间件
func HealthCheckMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.URL.Path == "/health" {
			c.JSON(200, gin.H{
				"status":    "ok",
				"timestamp": time.Now().Unix(),
				"service":   "cmdb-service",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// RequestSizeMiddleware 请求大小限制中间件
func RequestSizeMiddleware(maxSize int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.ContentLength > maxSize {
			c.JSON(413, gin.H{
				"code":    413,
				"message": "Request entity too large",
			})
			c.Abort()
			return
		}
		c.Next()
	}
}

// TimeoutMiddleware 请求超时中间件
func TimeoutMiddleware(timeout time.Duration, log *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置超时
		timer := time.NewTimer(timeout)
		defer timer.Stop()

		// 在goroutine中处理请求
		done := make(chan struct{})
		go func() {
			defer close(done)
			c.Next()
		}()

		select {
		case <-done:
			// 请求正常完成
			return
		case <-timer.C:
			// 请求超时
			log.WithFields(logrus.Fields{
				"method":     c.Request.Method,
				"path":       c.Request.URL.Path,
				"timeout_ms": timeout.Milliseconds(),
			}).Warn("Request timeout")

			c.JSON(408, gin.H{
				"code":    408,
				"message": "Request timeout",
			})
			c.Abort()
			return
		}
	}
}

// ResponseHeadersMiddleware 响应头中间件
func ResponseHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 添加API版本信息
		c.Header("X-API-Version", "v1.0.0")

		// 添加服务信息
		c.Header("X-Service", "cmdb-service")

		// 添加请求ID（如果存在）
		if requestID := c.GetHeader("X-Request-ID"); requestID != "" {
			c.Header("X-Request-ID", requestID)
		}

		// 添加响应时间
		start := time.Now()
		c.Next()

		duration := time.Since(start)
		c.Header("X-Response-Time", strconv.FormatInt(duration.Milliseconds(), 10)+"ms")
	}
}

// CompressionMiddleware 响应压缩中间件（简单实现）
func CompressionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查客户端是否支持压缩
		acceptEncoding := c.GetHeader("Accept-Encoding")
		if acceptEncoding == "" {
			c.Next()
			return
		}

		// 这里可以集成gzip压缩库
		// 简单实现，实际项目中应该使用专门的压缩中间件
		c.Next()
	}
}

// CacheControlMiddleware 缓存控制中间件
func CacheControlMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 根据请求类型设置缓存策略
		switch c.Request.Method {
		case "GET":
			// GET请求可以缓存
			c.Header("Cache-Control", "public, max-age=300") // 5分钟
		case "POST", "PUT", "DELETE":
			// 修改操作不缓存
			c.Header("Cache-Control", "no-cache, no-store, must-revalidate")
			c.Header("Pragma", "no-cache")
			c.Header("Expires", "0")
		}

		c.Next()
	}
}
