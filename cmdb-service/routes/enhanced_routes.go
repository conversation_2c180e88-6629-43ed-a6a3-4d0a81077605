package routes

import (
	"time"

	"github.com/devops-microservices/cmdb-service/config"
	"github.com/devops-microservices/cmdb-service/controllers"
	"github.com/devops-microservices/cmdb-service/middlewares"
	"github.com/devops-microservices/cmdb-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/gorm"
)

// SetupEnhancedRoutes 设置增强的路由配置
func SetupEnhancedRoutes(router *gin.Engine, db *gorm.DB, cfg *config.Config, log *logrus.Logger) {
	// 初始化性能监控器
	perfMonitor := middlewares.NewPerformanceMonitor(log)
	
	// ============== 全局中间件 ==============
	// 错误恢复中间件
	router.Use(middlewares.ErrorMiddleware(log))
	
	// 性能监控中间件
	router.Use(perfMonitor.PerformanceMiddleware())
	
	// 请求日志中间件
	router.Use(middlewares.RequestLogger(log))
	
	// CORS中间件
	router.Use(middlewares.CORSMiddleware())
	
	// 安全头中间件
	router.Use(middlewares.SecurityHeadersMiddleware())
	
	// 响应头中间件
	router.Use(middlewares.ResponseHeadersMiddleware())
	
	// 缓存控制中间件
	router.Use(middlewares.CacheControlMiddleware())
	
	// 请求大小限制中间件（10MB）
	router.Use(middlewares.RequestSizeMiddleware(10 * 1024 * 1024))
	
	// 请求超时中间件（30秒）
	router.Use(middlewares.TimeoutMiddleware(30*time.Second, log))
	
	// 健康检查中间件
	router.Use(middlewares.HealthCheckMiddleware())
	
	// ============== 初始化服务 ==============
	cmdbService := services.NewCMDBService(db, log)
	enhancedService := services.NewEnhancedCMDBService(db, log)
	auditService := services.NewAuditService(db, log)
	wsService := services.NewWebSocketService(log)
	
	// ============== 初始化控制器 ==============
	cmdbController := controllers.NewCMDBController(cmdbService, log, db)
	appController := controllers.NewApplicationController(db, cfg)
	regionController := controllers.NewRegionController(db)
	devLanguageController := controllers.NewDevLanguageController(db)
	kubernetesController := controllers.NewKubernetesController(db, cfg, auditService, wsService)
	pipelineController := controllers.NewPipelineController(cmdbService)
	
	// 创建认证中间件
	authMiddleware := middlewares.JWTAuthMiddleware(cfg, log)
	
	// ============== 公共路由（无需认证） ==============
	
	// 健康检查端点（详细版本）
	router.GET("/health", func(c *gin.Context) {
		// 检查数据库连接
		healthChecker := enhancedService.(*services.EnhancedCMDBService)
		dbHealth := "ok"
		if err := healthChecker.CheckHealth(c.Request.Context()); err != nil {
			dbHealth = "error: " + err.Error()
		}
		
		// 获取连接池统计
		connStats, _ := healthChecker.GetConnectionStats()
		
		c.JSON(200, gin.H{
			"status":      "ok",
			"service":     "cmdb-service",
			"version":     "v1.0.0",
			"timestamp":   time.Now().Unix(),
			"database":    dbHealth,
			"connections": connStats,
		})
	})
	
	// 性能指标端点
	router.GET("/metrics", func(c *gin.Context) {
		metrics := perfMonitor.GetMetrics()
		c.JSON(200, gin.H{
			"metrics": metrics,
		})
	})
	
	// Swagger文档路由
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
	
	// ============== API v1 路由（需要认证） ==============
	v1 := router.Group("/api/v1/cmdb")
	v1.Use(authMiddleware)
	{
		// Git相关路由
		setupGitRoutes(v1, appController)
		
		// Harbor相关路由
		v1.GET("/harbor", appController.GetHarbor)
		
		// 流水线相关路由
		setupPipelineRoutes(v1, pipelineController)
		
		// 产品相关路由
		setupProductRoutes(v1, cmdbController)
		
		// 环境相关路由
		setupEnvironmentRoutes(v1, cmdbController)
		
		// 区域相关路由
		setupRegionRoutes(v1, cmdbController, regionController)
		
		// 项目相关路由
		setupProjectRoutes(v1, cmdbController)
		
		// 应用相关路由
		setupApplicationRoutes(v1, cmdbController)
		
		// Kubernetes相关路由
		setupKubernetesRoutes(v1, cmdbController, kubernetesController)
		
		// 开发语言相关路由
		setupLanguageRoutes(v1, cmdbController, devLanguageController)
		
		// 数据中心相关路由
		setupDataCenterRoutes(v1, cmdbController)
	}
	
	// ============== API v2 路由（增强版本） ==============
	v2 := router.Group("/api/v2/cmdb")
	v2.Use(authMiddleware)
	{
		// 产品相关路由（增强版本）
		setupEnhancedProductRoutes(v2, enhancedService)
	}
	
	// 打印路由信息
	printRouteInfo(router, log)
}

// setupGitRoutes 设置Git相关路由
func setupGitRoutes(group *gin.RouterGroup, controller *controllers.ApplicationController) {
	gitGroup := group.Group("/git")
	{
		repoGroup := gitGroup.Group("/repo")
		{
			repoGroup.GET("", controller.GetRepos)
			repoGroup.GET("/branches", controller.GetBranches)
		}
	}
}

// setupPipelineRoutes 设置流水线相关路由
func setupPipelineRoutes(group *gin.RouterGroup, controller *controllers.PipelineController) {
	pipelineGroup := group.Group("/pipeline")
	{
		pipelineGroup.GET("", controller.GetPipelineList)
		pipelineGroup.GET("/:id", controller.GetPipeline)
		pipelineGroup.POST("", controller.CreatePipeline)
		pipelineGroup.PUT("/:id", controller.UpdatePipeline)
		pipelineGroup.DELETE("/:id", controller.DeletePipeline)
		pipelineGroup.GET("/inherit/:source_type/:source_id", controller.GetPipelineInheritance)
		pipelineGroup.POST("/:id/clone", controller.ClonePipeline)
		pipelineGroup.GET("/name/:name", controller.GetPipelineByName)
	}
}

// setupProductRoutes 设置产品相关路由
func setupProductRoutes(group *gin.RouterGroup, controller *controllers.CMDBController) {
	productGroup := group.Group("/product")
	{
		productGroup.GET("", controller.GetProducts)
		productGroup.GET("/:id/projects", controller.GetProductProjects)
		productGroup.GET("/:id", controller.GetProduct)
		productGroup.POST("", controller.CreateProduct)
		productGroup.PUT("/:id", controller.UpdateProduct)
		productGroup.DELETE("/:id", controller.DeleteProduct)
	}
}

// setupEnvironmentRoutes 设置环境相关路由
func setupEnvironmentRoutes(group *gin.RouterGroup, controller *controllers.CMDBController) {
	environmentGroup := group.Group("/environment")
	{
		environmentGroup.GET("", controller.GetEnvironments)
		environmentGroup.GET("/:id", controller.GetEnvironment)
		environmentGroup.POST("", controller.CreateEnvironment)
		environmentGroup.PUT("/:id", controller.UpdateEnvironment)
		environmentGroup.DELETE("/:id", controller.DeleteEnvironment)
	}
}

// setupRegionRoutes 设置区域相关路由
func setupRegionRoutes(group *gin.RouterGroup, cmdbController *controllers.CMDBController, regionController *controllers.RegionController) {
	// 使用CMDB控制器的路由
	group.GET("/region", cmdbController.GetRegions)
	
	// 使用专门的区域控制器的路由
	regionGroup := group.Group("/regions")
	{
		regionGroup.GET("", regionController.GetRegions)
		regionGroup.GET("/:id", regionController.GetRegion)
		regionGroup.POST("", regionController.CreateRegion)
		regionGroup.PUT("/:id", regionController.UpdateRegion)
		regionGroup.DELETE("/:id", regionController.DeleteRegion)
	}
}

// setupProjectRoutes 设置项目相关路由
func setupProjectRoutes(group *gin.RouterGroup, controller *controllers.CMDBController) {
	projectGroup := group.Group("/project")
	{
		projectGroup.GET("", controller.GetProjects)
		projectGroup.GET("/:id", controller.GetProject)
		projectGroup.POST("", controller.CreateProject)
		projectGroup.PUT("/:id", controller.UpdateProject)
		projectGroup.DELETE("/:id", controller.DeleteProject)
	}
}

// setupApplicationRoutes 设置应用相关路由
func setupApplicationRoutes(group *gin.RouterGroup, controller *controllers.CMDBController) {
	appGroup := group.Group("/app")
	{
		// 微应用路由
		appGroup.GET("", controller.GetMicroApps)
		appGroup.GET("/:id", controller.GetMicroApp)
		appGroup.POST("", controller.CreateMicroApp)
		
		microappGroup := appGroup.Group("/microapp")
		{
			microappGroup.PUT("/:id", controller.UpdateMicroApp)
			microappGroup.DELETE("/:id", controller.DeleteMicroApp)
			microappGroup.POST("/:id/favorite", controller.FavoriteMicroApp)
			microappGroup.POST("/:id/deploy", controller.DeployMicroApp)
			microappGroup.GET("/:id/deploy/history", controller.GetDeployHistory)
			microappGroup.GET("/:id/health", controller.GetMicroAppHealth)
			microappGroup.GET("/:id/resources", controller.GetMicroAppResources)
		}
		
		// 应用服务路由
		serviceGroup := appGroup.Group("/service")
		{
			serviceGroup.GET("", controller.GetAppInfos)
			serviceGroup.GET("/:id", controller.GetAppInfo)
			serviceGroup.POST("", controller.CreateAppInfo)
			serviceGroup.PUT("/:id", controller.UpdateAppInfo)
			serviceGroup.DELETE("/:id", controller.DeleteAppInfo)
		}
	}
}

// setupKubernetesRoutes 设置Kubernetes相关路由
func setupKubernetesRoutes(group *gin.RouterGroup, cmdbController *controllers.CMDBController, kubernetesController *controllers.KubernetesController) {
	// CMDB控制器的K8s路由
	group.GET("/kubernetes", cmdbController.GetKubernetesClusters)
	
	// 专门的K8s控制器路由
	k8sGroup := group.Group("/k8s")
	{
		k8sGroup.GET("/clusters", kubernetesController.GetClusters)
		k8sGroup.GET("/clusters/:cluster_id/namespaces", kubernetesController.GetNamespaces)
		k8sGroup.GET("/clusters/:cluster_id/workloads", kubernetesController.GetWorkloads)
		k8sGroup.GET("/clusters/:cluster_id/services", kubernetesController.GetServices)
		k8sGroup.GET("/clusters/:cluster_id/pods", kubernetesController.GetPods)
		k8sGroup.GET("/clusters/:cluster_id/nodes", kubernetesController.GetNodes)
		k8sGroup.GET("/clusters/:cluster_id/metrics", kubernetesController.GetClusterMetrics)
		k8sGroup.GET("/clusters/:cluster_id/health", kubernetesController.GetClusterHealth)
	}
}

// setupLanguageRoutes 设置开发语言相关路由
func setupLanguageRoutes(group *gin.RouterGroup, cmdbController *controllers.CMDBController, devLanguageController *controllers.DevLanguageController) {
	// CMDB控制器的语言路由
	group.GET("/app/language", cmdbController.GetDevLanguages)
	
	// 专门的语言控制器路由
	languageGroup := group.Group("/language")
	{
		languageGroup.GET("", devLanguageController.GetLanguages)
		languageGroup.GET("/:id", devLanguageController.GetLanguage)
		languageGroup.POST("", devLanguageController.CreateLanguage)
		languageGroup.PUT("/:id", devLanguageController.UpdateLanguage)
		languageGroup.DELETE("/:id", devLanguageController.DeleteLanguage)
	}
}

// setupDataCenterRoutes 设置数据中心相关路由
func setupDataCenterRoutes(group *gin.RouterGroup, controller *controllers.CMDBController) {
	dataCenterGroup := group.Group("/asset/datacenter")
	{
		dataCenterGroup.GET("", controller.GetDataCenters)
		dataCenterGroup.GET("/:id", controller.GetDataCenter)
		dataCenterGroup.POST("", controller.CreateDataCenter)
		dataCenterGroup.PUT("/:id", controller.UpdateDataCenter)
		dataCenterGroup.DELETE("/:id", controller.DeleteDataCenter)
	}
}

// setupEnhancedProductRoutes 设置增强的产品相关路由
func setupEnhancedProductRoutes(group *gin.RouterGroup, service *services.EnhancedCMDBService) {
	// 这里可以添加使用增强服务的路由
	// 例如：带有更详细验证和事务处理的产品管理接口
}

// printRouteInfo 打印路由信息
func printRouteInfo(router *gin.Engine, log *logrus.Logger) {
	routes := router.Routes()
	log.WithField("total_routes", len(routes)).Info("Routes registered")
	
	// 按方法分组统计
	methodCount := make(map[string]int)
	for _, route := range routes {
		methodCount[route.Method]++
	}
	
	for method, count := range methodCount {
		log.WithFields(logrus.Fields{
			"method": method,
			"count":  count,
		}).Debug("Route method statistics")
	}
}
