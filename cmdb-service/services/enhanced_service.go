package services

import (
	"context"
	"fmt"
	"time"

	"github.com/devops-microservices/cmdb-service/models"
	"github.com/devops-microservices/cmdb-service/utils"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedCMDBService 增强的CMDB服务
type EnhancedCMDBService struct {
	db             *gorm.DB
	log            *logrus.Logger
	txManager      *utils.TransactionManager
	queryOptimizer *utils.QueryOptimizer
	healthChecker  *utils.DatabaseHealthChecker
}

// NewEnhancedCMDBService 创建增强的CMDB服务
func NewEnhancedCMDBService(db *gorm.DB, log *logrus.Logger) *EnhancedCMDBService {
	return &EnhancedCMDBService{
		db:             db,
		log:            log,
		txManager:      utils.NewTransactionManager(db, log),
		queryOptimizer: utils.NewQueryOptimizer(db, log),
		healthChecker:  utils.NewDatabaseHealthChecker(db, log),
	}
}

// ProductService 产品服务增强功能
type ProductService struct {
	*EnhancedCMDBService
}

// CreateProductWithValidation 创建产品（带完整验证）
func (ps *ProductService) CreateProductWithValidation(ctx context.Context, req *models.ProductCreateRequest, creatorID uint) (*models.Product, error) {
	// 验证产品代码唯一性
	if err := ps.validateProductCodeUnique(ctx, req.ProductCode, 0); err != nil {
		return nil, err
	}

	// 验证产品名称唯一性
	if err := ps.validateProductNameUnique(ctx, req.Name, 0); err != nil {
		return nil, err
	}

	// 验证区域是否存在
	if req.RegionID != nil {
		if err := ps.validateRegionExists(ctx, *req.RegionID); err != nil {
			return nil, err
		}
	}

	// 在事务中创建产品
	result, err := ps.txManager.WithTransactionAndResult(ctx, func(tx *gorm.DB) (interface{}, error) {
		product := &models.Product{
			ProductCode: req.ProductCode,
			Name:        req.Name,
			RegionID:    req.RegionID,
			Description: req.Description,
			NamePrefix:  req.NamePrefix,
			Managers:    req.Managers,
		}

		if err := tx.Create(product).Error; err != nil {
			ps.log.WithError(err).Error("Failed to create product")
			return nil, fmt.Errorf("创建产品失败: %w", err)
		}

		// 预加载关联数据
		if err := tx.Preload("Region").First(product, product.ID).Error; err != nil {
			ps.log.WithError(err).Error("Failed to preload product relations")
			return nil, fmt.Errorf("加载产品关联数据失败: %w", err)
		}

		ps.log.WithFields(logrus.Fields{
			"product_id":   product.ID,
			"product_code": product.ProductCode,
			"creator_id":   creatorID,
		}).Info("Product created successfully")

		return product, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(*models.Product), nil
}

// UpdateProductWithValidation 更新产品（带完整验证）
func (ps *ProductService) UpdateProductWithValidation(ctx context.Context, id uint, updates map[string]interface{}) (*models.Product, error) {
	// 验证产品是否存在
	existingProduct, err := ps.getProductByID(ctx, id)
	if err != nil {
		return nil, err
	}

	// 验证更新数据
	if productCode, ok := updates["product_code"].(string); ok && productCode != "" {
		if err := ps.validateProductCodeUnique(ctx, productCode, id); err != nil {
			return nil, err
		}
	}

	if name, ok := updates["name"].(string); ok && name != "" {
		if err := ps.validateProductNameUnique(ctx, name, id); err != nil {
			return nil, err
		}
	}

	if regionID, ok := updates["region_id"].(uint); ok && regionID > 0 {
		if err := ps.validateRegionExists(ctx, regionID); err != nil {
			return nil, err
		}
	}

	// 在事务中更新产品
	result, err := ps.txManager.WithTransactionAndResult(ctx, func(tx *gorm.DB) (interface{}, error) {
		// 添加更新时间
		updates["updated_at"] = time.Now()

		if err := tx.Model(existingProduct).Updates(updates).Error; err != nil {
			ps.log.WithError(err).Error("Failed to update product")
			return nil, fmt.Errorf("更新产品失败: %w", err)
		}

		// 重新加载产品数据
		if err := tx.Preload("Region").First(existingProduct, id).Error; err != nil {
			ps.log.WithError(err).Error("Failed to reload product after update")
			return nil, fmt.Errorf("重新加载产品数据失败: %w", err)
		}

		ps.log.WithFields(logrus.Fields{
			"product_id": id,
			"updates":    updates,
		}).Info("Product updated successfully")

		return existingProduct, nil
	})

	if err != nil {
		return nil, err
	}

	return result.(*models.Product), nil
}

// DeleteProductWithValidation 删除产品（带关联检查）
func (ps *ProductService) DeleteProductWithValidation(ctx context.Context, id uint) error {
	// 验证产品是否存在
	product, err := ps.getProductByID(ctx, id)
	if err != nil {
		return err
	}

	// 检查是否有关联的项目
	var projectCount int64
	if err := ps.db.WithContext(ctx).Model(&models.Project{}).Where("product_id = ?", id).Count(&projectCount).Error; err != nil {
		ps.log.WithError(err).Error("Failed to count related projects")
		return fmt.Errorf("检查关联项目失败: %w", err)
	}

	if projectCount > 0 {
		return fmt.Errorf("无法删除产品，存在 %d 个关联项目", projectCount)
	}

	// 检查是否有关联的微应用
	var appCount int64
	if err := ps.db.WithContext(ctx).Model(&models.MicroApp{}).Where("product_id = ?", id).Count(&appCount).Error; err != nil {
		ps.log.WithError(err).Error("Failed to count related micro apps")
		return fmt.Errorf("检查关联微应用失败: %w", err)
	}

	if appCount > 0 {
		return fmt.Errorf("无法删除产品，存在 %d 个关联微应用", appCount)
	}

	// 在事务中删除产品
	return ps.txManager.WithTransaction(ctx, func(tx *gorm.DB) error {
		if err := tx.Delete(product).Error; err != nil {
			ps.log.WithError(err).Error("Failed to delete product")
			return fmt.Errorf("删除产品失败: %w", err)
		}

		ps.log.WithFields(logrus.Fields{
			"product_id":   id,
			"product_code": product.ProductCode,
		}).Info("Product deleted successfully")

		return nil
	})
}

// GetProductsWithSearch 获取产品列表（带搜索和分页）
func (ps *ProductService) GetProductsWithSearch(ctx context.Context, page, pageSize int, search string, regionID *uint) ([]*models.Product, int64, error) {
	query := ps.db.WithContext(ctx).Model(&models.Product{})

	// 添加搜索条件
	if search != "" {
		searchFields := []string{"name", "product_code", "description"}
		query = ps.queryOptimizer.BuildSearchQuery(query, searchFields, search)
	}

	// 添加区域过滤
	if regionID != nil && *regionID > 0 {
		query = query.Where("region_id = ?", *regionID)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		ps.log.WithError(err).Error("Failed to count products")
		return nil, 0, fmt.Errorf("获取产品总数失败: %w", err)
	}

	// 添加分页和排序
	allowedSortFields := []string{"id", "name", "product_code", "created_at", "updated_at"}
	query = ps.queryOptimizer.AddSorting(query, "created_at", "DESC", allowedSortFields)
	query = ps.queryOptimizer.AddPagination(query, page, pageSize)

	// 预加载关联数据
	query = query.Preload("Region")

	var products []*models.Product
	if err := query.Find(&products).Error; err != nil {
		ps.log.WithError(err).Error("Failed to fetch products")
		return nil, 0, fmt.Errorf("获取产品列表失败: %w", err)
	}

	return products, total, nil
}

// 私有辅助方法

func (ps *ProductService) getProductByID(ctx context.Context, id uint) (*models.Product, error) {
	var product models.Product
	if err := ps.db.WithContext(ctx).Preload("Region").First(&product, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("产品不存在")
		}
		ps.log.WithError(err).Error("Failed to get product by ID")
		return nil, fmt.Errorf("获取产品失败: %w", err)
	}
	return &product, nil
}

func (ps *ProductService) validateProductCodeUnique(ctx context.Context, code string, excludeID uint) error {
	var count int64
	query := ps.db.WithContext(ctx).Model(&models.Product{}).Where("product_code = ?", code)
	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	if err := query.Count(&count).Error; err != nil {
		ps.log.WithError(err).Error("Failed to check product code uniqueness")
		return fmt.Errorf("检查产品代码唯一性失败: %w", err)
	}

	if count > 0 {
		return fmt.Errorf("产品代码 '%s' 已存在", code)
	}

	return nil
}

func (ps *ProductService) validateProductNameUnique(ctx context.Context, name string, excludeID uint) error {
	var count int64
	query := ps.db.WithContext(ctx).Model(&models.Product{}).Where("name = ?", name)
	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	if err := query.Count(&count).Error; err != nil {
		ps.log.WithError(err).Error("Failed to check product name uniqueness")
		return fmt.Errorf("检查产品名称唯一性失败: %w", err)
	}

	if count > 0 {
		return fmt.Errorf("产品名称 '%s' 已存在", name)
	}

	return nil
}

func (ps *ProductService) validateRegionExists(ctx context.Context, regionID uint) error {
	var count int64
	if err := ps.db.WithContext(ctx).Model(&models.Region{}).Where("id = ?", regionID).Count(&count).Error; err != nil {
		ps.log.WithError(err).Error("Failed to check region existence")
		return fmt.Errorf("检查区域是否存在失败: %w", err)
	}

	if count == 0 {
		return fmt.Errorf("区域 ID %d 不存在", regionID)
	}

	return nil
}
