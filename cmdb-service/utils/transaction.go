package utils

import (
	"context"
	"fmt"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// TransactionManager 事务管理器
type TransactionManager struct {
	db  *gorm.DB
	log *logrus.Logger
}

// NewTransactionManager 创建事务管理器
func NewTransactionManager(db *gorm.DB, log *logrus.Logger) *TransactionManager {
	return &TransactionManager{
		db:  db,
		log: log,
	}
}

// WithTransaction 在事务中执行操作
func (tm *TransactionManager) WithTransaction(ctx context.Context, fn func(*gorm.DB) error) error {
	return tm.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		tm.log.Debug("Transaction started")
		
		err := fn(tx)
		if err != nil {
			tm.log.WithError(err).Error("Transaction failed, rolling back")
			return err
		}
		
		tm.log.Debug("Transaction committed successfully")
		return nil
	})
}

// WithTransactionAndResult 在事务中执行操作并返回结果
func (tm *TransactionManager) WithTransactionAndResult(ctx context.Context, fn func(*gorm.DB) (interface{}, error)) (interface{}, error) {
	var result interface{}
	
	err := tm.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		tm.log.Debug("Transaction with result started")
		
		var err error
		result, err = fn(tx)
		if err != nil {
			tm.log.WithError(err).Error("Transaction with result failed, rolling back")
			return err
		}
		
		tm.log.Debug("Transaction with result committed successfully")
		return nil
	})
	
	return result, err
}

// BatchOperation 批量操作管理器
type BatchOperation struct {
	db        *gorm.DB
	log       *logrus.Logger
	batchSize int
}

// NewBatchOperation 创建批量操作管理器
func NewBatchOperation(db *gorm.DB, log *logrus.Logger, batchSize int) *BatchOperation {
	if batchSize <= 0 {
		batchSize = 100 // 默认批量大小
	}
	
	return &BatchOperation{
		db:        db,
		log:       log,
		batchSize: batchSize,
	}
}

// BatchCreate 批量创建
func (bo *BatchOperation) BatchCreate(ctx context.Context, items interface{}) error {
	return bo.db.WithContext(ctx).CreateInBatches(items, bo.batchSize).Error
}

// BatchUpdate 批量更新
func (bo *BatchOperation) BatchUpdate(ctx context.Context, model interface{}, updates map[string]interface{}, where string, args ...interface{}) error {
	return bo.db.WithContext(ctx).Model(model).Where(where, args...).Updates(updates).Error
}

// BatchDelete 批量删除
func (bo *BatchOperation) BatchDelete(ctx context.Context, model interface{}, where string, args ...interface{}) error {
	return bo.db.WithContext(ctx).Where(where, args...).Delete(model).Error
}

// DatabaseHealthChecker 数据库健康检查器
type DatabaseHealthChecker struct {
	db  *gorm.DB
	log *logrus.Logger
}

// NewDatabaseHealthChecker 创建数据库健康检查器
func NewDatabaseHealthChecker(db *gorm.DB, log *logrus.Logger) *DatabaseHealthChecker {
	return &DatabaseHealthChecker{
		db:  db,
		log: log,
	}
}

// CheckHealth 检查数据库健康状态
func (dhc *DatabaseHealthChecker) CheckHealth(ctx context.Context) error {
	sqlDB, err := dhc.db.DB()
	if err != nil {
		dhc.log.WithError(err).Error("Failed to get underlying sql.DB")
		return fmt.Errorf("failed to get database connection: %w", err)
	}
	
	// 检查连接是否可用
	if err := sqlDB.PingContext(ctx); err != nil {
		dhc.log.WithError(err).Error("Database ping failed")
		return fmt.Errorf("database ping failed: %w", err)
	}
	
	// 检查连接池状态
	stats := sqlDB.Stats()
	dhc.log.WithFields(logrus.Fields{
		"open_connections":     stats.OpenConnections,
		"in_use":              stats.InUse,
		"idle":                stats.Idle,
		"wait_count":          stats.WaitCount,
		"wait_duration":       stats.WaitDuration,
		"max_idle_closed":     stats.MaxIdleClosed,
		"max_lifetime_closed": stats.MaxLifetimeClosed,
	}).Debug("Database connection pool stats")
	
	// 检查是否有过多的等待连接
	if stats.WaitCount > 100 {
		dhc.log.Warn("High number of connection waits detected")
	}
	
	return nil
}

// GetConnectionStats 获取连接池统计信息
func (dhc *DatabaseHealthChecker) GetConnectionStats() (map[string]interface{}, error) {
	sqlDB, err := dhc.db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}
	
	stats := sqlDB.Stats()
	return map[string]interface{}{
		"open_connections":     stats.OpenConnections,
		"in_use":              stats.InUse,
		"idle":                stats.Idle,
		"wait_count":          stats.WaitCount,
		"wait_duration_ms":    stats.WaitDuration.Milliseconds(),
		"max_idle_closed":     stats.MaxIdleClosed,
		"max_lifetime_closed": stats.MaxLifetimeClosed,
	}, nil
}

// QueryOptimizer 查询优化器
type QueryOptimizer struct {
	db  *gorm.DB
	log *logrus.Logger
}

// NewQueryOptimizer 创建查询优化器
func NewQueryOptimizer(db *gorm.DB, log *logrus.Logger) *QueryOptimizer {
	return &QueryOptimizer{
		db:  db,
		log: log,
	}
}

// OptimizeQuery 优化查询
func (qo *QueryOptimizer) OptimizeQuery(query *gorm.DB) *gorm.DB {
	// 添加查询优化逻辑
	return query.Session(&gorm.Session{
		PrepareStmt: true, // 使用预编译语句
	})
}

// BuildSearchQuery 构建搜索查询
func (qo *QueryOptimizer) BuildSearchQuery(query *gorm.DB, searchFields []string, searchTerm string) *gorm.DB {
	if searchTerm == "" || len(searchFields) == 0 {
		return query
	}
	
	// 构建OR查询
	for i, field := range searchFields {
		if i == 0 {
			query = query.Where(fmt.Sprintf("%s LIKE ?", field), "%"+searchTerm+"%")
		} else {
			query = query.Or(fmt.Sprintf("%s LIKE ?", field), "%"+searchTerm+"%")
		}
	}
	
	return query
}

// AddPagination 添加分页
func (qo *QueryOptimizer) AddPagination(query *gorm.DB, page, pageSize int) *gorm.DB {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}
	
	offset := (page - 1) * pageSize
	return query.Offset(offset).Limit(pageSize)
}

// AddSorting 添加排序
func (qo *QueryOptimizer) AddSorting(query *gorm.DB, sortField, sortOrder string, allowedFields []string) *gorm.DB {
	if sortField == "" {
		return query.Order("created_at DESC") // 默认排序
	}
	
	// 检查排序字段是否在允许列表中
	allowed := false
	for _, field := range allowedFields {
		if field == sortField {
			allowed = true
			break
		}
	}
	
	if !allowed {
		qo.log.WithField("sort_field", sortField).Warn("Invalid sort field, using default")
		return query.Order("created_at DESC")
	}
	
	// 检查排序方向
	if sortOrder != "ASC" && sortOrder != "DESC" {
		sortOrder = "DESC"
	}
	
	return query.Order(fmt.Sprintf("%s %s", sortField, sortOrder))
}
