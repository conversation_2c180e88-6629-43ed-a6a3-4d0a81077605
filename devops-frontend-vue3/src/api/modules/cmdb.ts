import request from '@/utils/request'
import type { APIResponse, BaseModel, JSONField, PaginatedResponse } from './base'

// ===== 通用类型定义 =====

export interface PaginationParams {
    page?: number
    page_size?: number
    search?: string
    sort_by?: string
    sort_order?: 'asc' | 'desc'
}

export interface CMDBResponse<T = any> {
    code: number
    data: T
    message?: string
    timestamp?: string
}

export interface CMDBListResponse<T = any> {
    code: number
    data: {
        items: T[]
        total: number
        page: number
        page_size: number
        total_pages: number
    }
    message?: string
}

// ===== CMDB业务模型类型定义 =====

// 开发语言
export interface DevLanguage extends BaseModel {
    name: string
    language_code?: string
    description?: string
    dockerfile_template?: string
    pipeline_id?: number
    pipeline_template?: JSONField
    icon?: string
    color?: string
    sort_order: number
}

// 区域
export interface Region extends BaseModel {
    name: string
    region_code?: string
    description?: string
    sort_order: number
}

// 数据中心
export interface DataCenter extends BaseModel {
    name: string
    data_center_code?: string
    region_id?: number
    region?: Region
    address?: string
    description?: string
}

// 产品
export interface Product extends BaseModel {
    product_code: string
    name: string
    region_id?: number
    region?: Region
    description?: string
    name_prefix?: string
    managers?: JSONField
}

// 环境
export interface Environment extends BaseModel {
    name: string
    environment_code?: string
    ticket_enabled: number
    merge_enabled: number
    template_settings?: JSONField
    allow_ci_branch?: JSONField
    allow_cd_branch?: JSONField
    extra_data?: JSONField
    description?: string
    sort_order: number
}

// 项目
export interface Project extends BaseModel {
    name: string
    project_code: string
    product_id?: number
    product?: Product
    creator_id?: number
    managers?: {
        manager?: {
            id: number
            username: string
            display_name: string
            email: string
        }
        developer?: {
            id: number
            username: string
            display_name: string
            email: string
        }
        tester?: {
            id: number
            username: string
            display_name: string
            email: string
        }
    }
    description?: string,
    notify_settings?: JSONField
}

// 流水线
export interface Pipeline extends BaseModel {
    name: string
    source_id?: number
    source_type?: string
    pipeline_type: string
    pipeline_config?: JSONField
    description?: string
    is_default: boolean
    version?: string
}

// 微应用
export interface MicroApp extends BaseModel {
    app_code: string
    name: string
    project_id?: number
    project?: Project
    language_id?: number
    language?: DevLanguage
    description?: string
    git_repo?: string
    git_branch?: string
    dockerfile_path?: string
    build_path?: string
    managers?: JSONField
    extra_data?: JSONField
}

// 应用服务
export interface AppService extends BaseModel {
    name: string
    app_id?: number
    app?: MicroApp
    environment_id?: number
    environment?: Environment
    kubernetes_id?: number
    namespace?: string
    service_port?: number
    replicas?: number
    cpu_request?: string
    cpu_limit?: string
    memory_request?: string
    memory_limit?: string
    config_data?: JSONField
    extra_data?: JSONField
}

// Kubernetes集群
export interface KubernetesCluster extends BaseModel {
    name: string
    cluster_code?: string
    region_id?: number
    region?: Region
    api_server?: string
    config?: JSONField
    description?: string
    is_default: boolean
}

// 创建请求类型
export interface ProductCreateRequest {
    product_code: string
    name: string
    region_id?: number
    description?: string
    name_prefix?: string
    managers?: any
}

export interface EnvironmentCreateRequest {
    name: string
    environment_code?: string
    ticket_enabled: number
    merge_enabled: number
    template_settings?: any
    branch_settings?: any
    extra_data?: any
    description?: string
    sort_order: number
}

export interface ProjectCreateRequest {
    name: string
    project_code: string
    product_id?: number
    managers?: any
    description?: string
    notify_settings?: any
}

export interface MicroAppCreateRequest {
    app_code: string
    name: string
    project_id?: number
    language_id?: number
    description?: string
    git_repo?: string
    git_branch?: string
    dockerfile_path?: string
    build_path?: string
    managers?: any
    extra_data?: any
}

// 查询参数类型
export interface DevLanguageListQuery extends PaginationParams {
    language_code?: string
}

export interface RegionListQuery extends PaginationParams {
    region_code?: string
}

export interface ProductListQuery extends PaginationParams {
    region_id?: number
    product_code?: string
}

export interface EnvironmentListQuery extends PaginationParams {
    environment_code?: string
}

export interface ProjectListQuery extends PaginationParams {
    product_id?: number
    project_code?: string
}

export interface MicroAppListQuery extends PaginationParams {
    project_id?: number
    language_id?: number
    app_code?: string
    category_name?: string
    is_online?: boolean
}

export interface AppInfoListQuery {
    page?: number
    page_size?: number
    unique_tag?: string
    app_id?: number
    environment_id?: number
    is_enabled?: number
}

// 创建请求类型
export interface DevLanguageCreateRequest {
    name: string
    language_code?: string
    description?: string
    dockerfile_template?: string
    pipeline_template?: JSONField
    icon?: string
    color?: string
    sort_order?: number
}

export interface RegionCreateRequest {
    name: string
    region_code?: string
    description?: string
    sort_order?: number
}

export interface DataCenterCreateRequest {
    name: string
    data_center_code?: string
    region_id?: number
    address?: string
    description?: string
}

export interface ProductCreateRequest {
    name: string
    product_code?: string
    region_id?: number
    description?: string
    name_prefix?: string
    managers?: JSONField
}

export interface EnvironmentCreateRequest {
    name: string
    environment_code?: string
    ticket_enabled?: number
    merge_enabled?: number
    template_settings?: JSONField
    allow_ci_branch?: JSONField
    allow_cd_branch?: JSONField
    extra_data?: JSONField
    description?: string
    sort_order?: number
}

export interface ProjectCreateRequest {
    project_code: string
    name: string
    product_id?: number
    managers?: JSONField
    description?: string
    notify_settings?: JSONField
    template_data?: JSONField
}

export interface MicroAppCreateRequest {
    app_code: string
    name: string
    project_id?: number
    repo_settings?: JSONField
    target_settings?: JSONField
    team_members?: JSONField
    category_name?: string
    template_data?: JSONField
    language?: string
    build_command?: string
    is_multi_app?: boolean
    multi_app_ids?: JSONField
    docker_settings?: JSONField
    is_online?: boolean
    description?: string
    notify_settings?: JSONField
    edit_permission?: JSONField
    deployment_type?: string
    module_settings?: JSONField
    port_settings?: JSONField
    scan_branch?: string
}

export interface AppInfoCreateRequest {
    unique_tag: string
    app_id?: number
    environment_id?: number
    branch_name?: string
    allow_ci_branch?: JSONField
    allow_cd_branch?: JSONField
    build_command?: string
    version_number?: string
    template_data?: JSONField
    pipeline_id?: number
    pipeline_type?: string
    pipeline_config?: JSONField
    is_enabled?: number
    description?: string
    edit_permission?: JSONField
    online_status?: number
    port_settings?: JSONField
    kubernetes_cluster_ids?: number[]
}

export interface PipelineCreateRequest {
    name: string
    source_id?: number
    source_type?: string
    pipeline_type: string
    pipeline_config?: JSONField
    description?: string
    is_default?: boolean
    version?: string
}

// ===== API 服务定义 =====

// 开发语言API
export const languageApi = {
    // 获取开发语言列表
    getLanguages: (params?: DevLanguageListQuery) => {
        return request.get<PaginatedResponse<DevLanguage>>('/v1/cmdb/language', { params })
    },

    // 获取单个开发语言
    getLanguage: (id: number) => {
        return request.get<APIResponse<DevLanguage>>(`/v1/cmdb/language/${id}`)
    },

    // 根据language_code获取开发语言
    getLanguageByCode: (language_code: string) => {
        return request.get<APIResponse<DevLanguage>>(`/v1/cmdb/language/code/${language_code}`)
    },

    // 创建开发语言
    createLanguage: (data: DevLanguageCreateRequest) => {
        return request.post<APIResponse<DevLanguage>>('/v1/cmdb/language', data)
    },

    // 更新开发语言
    updateLanguage: (id: number, data: Partial<DevLanguageCreateRequest>) => {
        return request.put<APIResponse<DevLanguage>>(`/v1/cmdb/language/${id}`, data)
    },

    // 删除开发语言
    deleteLanguage: (id: number) => {
        return request.delete<APIResponse<void>>(`/v1/cmdb/language/${id}`)
    },

    // 获取语言的Dockerfile
    getDockerfile: (id: number) => {
        return request.get<APIResponse<string>>(`/v1/cmdb/language/${id}/dockerfile`)
    },

    // 获取语言的Tekton Pipeline
    getPipeline: (id: number) => {
        return request.get<APIResponse<JSONField>>(`/v1/cmdb/language/${id}/pipeline`)
    },

    // 保存语言的Tekton Pipeline
    savePipeline: (id: number, data: JSONField) => {
        return request.put<APIResponse<void>>(`/v1/cmdb/language/${id}/pipeline`, data)
    }
}

// 区域API
export const regionApi = {
    // 获取区域列表
    getRegions: (params?: RegionListQuery) => {
        return request.get<PaginatedResponse<Region>>('/v1/cmdb/region', { params })
    },

    // 获取单个区域
    getRegion: (id: number) => {
        return request.get<APIResponse<Region>>(`/v1/cmdb/region/${id}`)
    },

    // 创建区域
    createRegion: (data: RegionCreateRequest) => {
        return request.post<APIResponse<Region>>('/v1/cmdb/region', data)
    },

    // 更新区域
    updateRegion: (id: number, data: Partial<RegionCreateRequest>) => {
        return request.put<APIResponse<Region>>(`/v1/cmdb/region/${id}`, data)
    },

    // 删除区域
    deleteRegion: (id: number) => {
        return request.delete<APIResponse<void>>(`/v1/cmdb/region/${id}`)
    },

    // 获取区域下的产品列表
    getRegionProducts: (regionId: number) => {
        return request.get<APIResponse<Product[]>>(`/v1/cmdb/region/product?region_id=${regionId}`)
    }
}

// 数据中心API
export const dataCenterApi = {
    // 获取数据中心列表
    getDataCenters: (params?: { page?: number; page_size?: number; name?: string; region_id?: number }) => {
        return request.get<PaginatedResponse<DataCenter>>('/v1/cmdb/datacenter', { params })
    },

    // 获取单个数据中心
    getDataCenter: (id: number) => {
        return request.get<APIResponse<DataCenter>>(`/v1/cmdb/datacenter/${id}`)
    },

    // 创建数据中心
    createDataCenter: (data: DataCenterCreateRequest) => {
        return request.post<APIResponse<DataCenter>>('/v1/cmdb/datacenter', data)
    },

    // 更新数据中心
    updateDataCenter: (id: number, data: Partial<DataCenterCreateRequest>) => {
        return request.put<APIResponse<DataCenter>>(`/v1/cmdb/datacenter/${id}`, data)
    },

    // 删除数据中心
    deleteDataCenter: (id: number) => {
        return request.delete<APIResponse<void>>(`/v1/cmdb/datacenter/${id}`)
    }
}

// 产品API
export const productApi = {
    // 获取产品列表
    getProducts: (params?: ProductListQuery) => {
        return request.get<PaginatedResponse<Product>>('/v1/cmdb/product', { params })
    },

    // 获取单个产品
    getProduct: (id: number) => {
        return request.get<APIResponse<Product>>(`/v1/cmdb/product/${id}`)
    },

    // 获取产品项目列表
    getProductProjects: (productId: number) => {
        return request.get<APIResponse<Project[]>>(`/v1/cmdb/product/${productId}/projects`)
    },

    // 创建产品
    createProduct: (data: ProductCreateRequest) => {
        return request.post<APIResponse<Product>>('/v1/cmdb/product', data)
    },

    // 更新产品
    updateProduct: (id: number, data: Partial<ProductCreateRequest>) => {
        return request.put<APIResponse<Product>>(`/v1/cmdb/product/${id}`, data)
    },

    // 删除产品
    deleteProduct: (id: number) => {
        return request.delete<APIResponse<void>>(`/v1/cmdb/product/${id}`)
    }
}

// 环境API
export const environmentApi = {
    // 获取环境列表
    getEnvironments: (params?: EnvironmentListQuery) => {
        return request.get<PaginatedResponse<Environment>>('/v1/cmdb/environment', { params })
    },

    // 获取单个环境
    getEnvironment: (id: number) => {
        return request.get<APIResponse<Environment>>(`/v1/cmdb/environment/${id}`)
    },

    // 创建环境
    createEnvironment: (data: EnvironmentCreateRequest) => {
        return request.post<APIResponse<Environment>>('/v1/cmdb/environment', data)
    },

    // 更新环境
    updateEnvironment: (id: number, data: Partial<EnvironmentCreateRequest>) => {
        return request.put<APIResponse<Environment>>(`/v1/cmdb/environment/${id}`, data)
    },

    // 删除环境
    deleteEnvironment: (id: number) => {
        return request.delete<APIResponse<void>>(`/v1/cmdb/environment/${id}`)
    }
}

// 项目API
export const projectApi = {
    // 获取项目列表
    getProjects: (params?: ProjectListQuery) => {
        return request.get<PaginatedResponse<Project>>('/v1/cmdb/project', { params })
    },

    // 获取单个项目
    getProject: (id: number) => {
        return request.get<APIResponse<Project>>(`/v1/cmdb/project/${id}`)
    },

    // 创建项目
    createProject: (data: ProjectCreateRequest) => {
        return request.post<APIResponse<Project>>('/v1/cmdb/project', data)
    },

    // 更新项目
    updateProject: (id: number, data: Partial<ProjectCreateRequest>) => {
        return request.put<APIResponse<Project>>(`/v1/cmdb/project/${id}`, data)
    },

    // 删除项目
    deleteProject: (id: number) => {
        return request.delete<APIResponse<void>>(`/v1/cmdb/project/${id}`)
    },

    // 获取项目机器人配置
    getRobot: () => {
        return request.get<APIResponse<any>>('/v1/cmdb/project/robot')
    },

    // 项目配置相关
    getProjectConfigs: (params?: { project_id?: number; environment_id?: number }) => {
        return request.get<APIResponse<any[]>>('/v1/cmdb/project/config', { params })
    },

    getProjectConfig: (id: number) => {
        return request.get<APIResponse<any>>(`/v1/cmdb/project/config/${id}`)
    },

    createProjectConfig: (data: any) => {
        return request.post<APIResponse<any>>('/v1/cmdb/project/config', data)
    },

    updateProjectConfig: (id: number, data: any) => {
        return request.put<APIResponse<any>>(`/v1/cmdb/project/config/${id}`, data)
    },

    deleteProjectConfig: (id: number) => {
        return request.delete<APIResponse<void>>(`/v1/cmdb/project/config/${id}`)
    },

    getProjectConfigInheritTemplate: (params?: any) => {
        return request.get<APIResponse<any>>('/v1/cmdb/project/config/template/inherit', { params })
    }
}

// 流水线API
export const pipelineApi = {
    // 获取流水线列表
    getPipelines: (params?: { name?: string; page?: number; page_size?: number }) => {
        return request.get<PaginatedResponse<Pipeline>>('/v1/cmdb/pipeline', { params })
    },

    // 获取单个流水线
    getPipeline: (id: number) => {
        return request.get<APIResponse<Pipeline>>(`/v1/cmdb/pipeline/${id}`)
    },

    // 根据名称获取流水线
    getPipelineByName: (name: string) => {
        return request.get<APIResponse<Pipeline>>(`/v1/cmdb/pipeline/name/${name}`)
    },

    // 创建流水线
    createPipeline: (data: PipelineCreateRequest) => {
        return request.post<APIResponse<Pipeline>>('/v1/cmdb/pipeline', data)
    },

    // 更新流水线
    updatePipeline: (id: number, data: Partial<PipelineCreateRequest>) => {
        return request.put<APIResponse<Pipeline>>(`/v1/cmdb/pipeline/${id}`, data)
    },

    // 删除流水线
    deletePipeline: (id: number) => {
        return request.delete<APIResponse<void>>(`/v1/cmdb/pipeline/${id}`)
    },

    // 继承流水线配置
    getInheritPipeline: (data: any) => {
        return request.get<APIResponse<any>>(`/v1/cmdb/pipeline/inherit/${data.source_type}/${data.source_id}`, data)
    }
}

// Git仓库API
export const gitApi = {
    // 获取仓库列表
    getRepos: (params?: { search?: string; page?: number; page_size?: number; project_id?: number }) => {
        return request.get<APIResponse<any[]>>('/v1/cmdb/git/repo', { params })
    },

    // 获取分支列表
    getBranches: (params: { repo_url: string, project_id: number }) => {
        return request.get<APIResponse<string[]>>('/v1/cmdb/git/repo/branches', { params })
    }
}

// Harbor API
export const harborApi = {
    // 获取Harbor项目列表
    getHarborProjects: (params?: { page?: number; page_size?: number; search?: string }) => {
        return request.get<APIResponse<any[]>>('/v1/cmdb/harbor?type=projects', { params })
    },

    // 获取Harbor镜像列表
    getHarborImages: (projectName: string, search?: string) => {
        return request.get<APIResponse<any[]>>(`/v1/cmdb/harbor?type=repos&project_name=${projectName}&search=${search || ''}`)
    },

    // 获取Harbor镜像标签列表
    getHarborImageTags: (projectName: string, image: string) => {
        const fullImageName = image.includes(projectName) ? image : `${projectName}/${image}`
        return request.get<APIResponse<any[]>>(`/v1/cmdb/harbor?type=tags&project_name=${projectName}&image=${fullImageName}`)
    }
}

// 微应用API
export const microAppApi = {
    // 获取微应用列表
    getMicroApps: (params?: MicroAppListQuery) => {
        return request.get<CMDBListResponse<MicroApp>>('/v1/cmdb/microapp', { params })
    },

    // 获取单个微应用
    getMicroApp: (id: number) => {
        return request.get<CMDBResponse<MicroApp>>(`/v1/cmdb/microapp/${id}`)
    },

    // 创建微应用
    createMicroApp: (data: MicroAppCreateRequest) => {
        return request.post<CMDBResponse<MicroApp>>('/v1/cmdb/microapp', data)
    },

    // 更新微应用
    updateMicroApp: (id: number, data: Partial<MicroAppCreateRequest>) => {
        return request.put<CMDBResponse<MicroApp>>(`/v1/cmdb/microapp/${id}`, data)
    },

    // 删除微应用
    deleteMicroApp: (id: number) => {
        return request.delete<CMDBResponse<void>>(`/v1/cmdb/microapp/${id}`)
    },

    // 批量删除微应用
    batchDeleteMicroApps: (ids: number[]) => {
        return request.delete<CMDBResponse<void>>('/v1/cmdb/microapp/batch', { data: { ids } })
    },

    // 获取微应用统计信息
    getMicroAppStats: () => {
        return request.get<CMDBResponse<{
            total: number
            by_language: Record<string, number>
            by_project: Record<string, number>
            by_status: Record<string, number>
        }>>('/v1/cmdb/microapp/stats')
    },

    // 导出微应用列表
    exportMicroApps: (params?: MicroAppListQuery) => {
        return request.get('/v1/cmdb/microapp/export', {
            params,
            responseType: 'blob'
        })
    },

    // 导入微应用
    importMicroApps: (file: File) => {
        const formData = new FormData()
        formData.append('file', file)
        return request.post<CMDBResponse<{
            success: number
            failed: number
            errors: string[]
        }>>('/v1/cmdb/microapp/import', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        })
    }
}

// 应用服务API
export const appServiceApi = {
    // 获取应用服务列表
    getAppServices: (params?: {
        page?: number
        page_size?: number
        search?: string
        app_id?: number
        environment_id?: number
    }) => {
        return request.get<CMDBListResponse<AppService>>('/v1/cmdb/appservice', { params })
    },

    // 获取单个应用服务
    getAppService: (id: number) => {
        return request.get<CMDBResponse<AppService>>(`/v1/cmdb/appservice/${id}`)
    },

    // 创建应用服务
    createAppService: (data: Partial<AppService>) => {
        return request.post<CMDBResponse<AppService>>('/v1/cmdb/appservice', data)
    },

    // 更新应用服务
    updateAppService: (id: number, data: Partial<AppService>) => {
        return request.put<CMDBResponse<AppService>>(`/v1/cmdb/appservice/${id}`, data)
    },

    // 删除应用服务
    deleteAppService: (id: number) => {
        return request.delete<CMDBResponse<void>>(`/v1/cmdb/appservice/${id}`)
    }
}

// Kubernetes集群API
export const kubernetesApi = {
    // 获取集群列表
    getClusters: (params?: PaginationParams) => {
        return request.get<CMDBListResponse<KubernetesCluster>>('/v1/cmdb/kubernetes', { params })
    },

    // 获取单个集群
    getCluster: (id: number) => {
        return request.get<CMDBResponse<KubernetesCluster>>(`/v1/cmdb/kubernetes/${id}`)
    },

    // 创建集群
    createCluster: (data: Partial<KubernetesCluster>) => {
        return request.post<CMDBResponse<KubernetesCluster>>('/v1/cmdb/kubernetes', data)
    },

    // 更新集群
    updateCluster: (id: number, data: Partial<KubernetesCluster>) => {
        return request.put<CMDBResponse<KubernetesCluster>>(`/v1/cmdb/kubernetes/${id}`, data)
    },

    // 删除集群
    deleteCluster: (id: number) => {
        return request.delete<CMDBResponse<void>>(`/v1/cmdb/kubernetes/${id}`)
    },

    // 测试集群连接
    testClusterConnection: (id: number) => {
        return request.post<CMDBResponse<{
            success: boolean
            message: string
            version?: string
            nodes?: number
        }>>(`/v1/cmdb/kubernetes/${id}/test`)
    }
}

// 默认导出
export default {
    languageApi,
    regionApi,
    dataCenterApi,
    productApi,
    environmentApi,
    projectApi,
    microAppApi,
    appServiceApi,
    kubernetesApi,
    pipelineApi,
    gitApi,
    harborApi
}