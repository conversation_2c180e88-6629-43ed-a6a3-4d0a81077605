import request from '@/utils/request'
import type { APIResponse, BaseModel, JSONField, PaginatedResponse } from './base'
import type { 
    Product, Environment, Project, MicroApp, Region, DevLanguage, DataCenter,
    ProductListQuery, EnvironmentListQuery, ProjectListQuery, MicroAppListQuery,
    ProductCreateRequest, EnvironmentCreateRequest, ProjectCreateRequest, MicroAppCreateRequest
} from './cmdb'

// ===== 增强的API响应类型 =====

export interface EnhancedAPIResponse<T = any> extends APIResponse<T> {
    timestamp?: number
    request_id?: string
    version?: string
}

export interface ValidationError {
    field: string
    message: string
    code?: string
}

export interface ErrorResponse {
    code: number
    message: string
    details?: string
    validation_errors?: ValidationError[]
    timestamp?: number
    request_id?: string
}

// ===== 增强的查询参数 =====

export interface EnhancedListQuery {
    page?: number
    page_size?: number
    search?: string
    sort_field?: string
    sort_order?: 'ASC' | 'DESC'
    filters?: Record<string, any>
}

export interface ProductEnhancedQuery extends EnhancedListQuery {
    region_id?: number
    has_projects?: boolean
    created_after?: string
    created_before?: string
}

export interface ProjectEnhancedQuery extends EnhancedListQuery {
    product_id?: number
    has_apps?: boolean
    manager_id?: number
}

export interface MicroAppEnhancedQuery extends EnhancedListQuery {
    project_id?: number
    product_id?: number
    language_code?: string
    category_name?: string
    deployment_type?: string
    is_enabled?: boolean
    is_favorite?: boolean
}

// ===== 统计和分析类型 =====

export interface CMDBStatistics {
    total_products: number
    total_projects: number
    total_apps: number
    total_environments: number
    apps_by_language: Record<string, number>
    apps_by_category: Record<string, number>
    projects_by_product: Record<string, number>
    recent_deployments: number
}

export interface HealthStatus {
    status: 'healthy' | 'warning' | 'error'
    database: string
    connections?: Record<string, any>
    timestamp: number
    version: string
}

// ===== 增强的API客户端 =====

class EnhancedCMDBApi {
    private baseURL = '/api/v1/cmdb'

    // 通用请求方法，带错误处理
    private async request<T>(
        method: 'GET' | 'POST' | 'PUT' | 'DELETE',
        url: string,
        data?: any,
        params?: any
    ): Promise<T> {
        try {
            const config: any = { params }
            if (data && (method === 'POST' || method === 'PUT')) {
                config.data = data
            }

            const response = await request({
                method: method.toLowerCase(),
                url: `${this.baseURL}${url}`,
                ...config
            })

            return response.data
        } catch (error: any) {
            // 增强错误处理
            if (error.response?.data) {
                const errorData = error.response.data as ErrorResponse
                throw new Error(errorData.message || '请求失败')
            }
            throw error
        }
    }

    // ===== 产品相关API =====
    
    async getProducts(params?: ProductEnhancedQuery): Promise<PaginatedResponse<Product>> {
        return this.request('GET', '/product', undefined, params)
    }

    async getProduct(id: number): Promise<EnhancedAPIResponse<Product>> {
        return this.request('GET', `/product/${id}`)
    }

    async createProduct(data: ProductCreateRequest): Promise<EnhancedAPIResponse<Product>> {
        return this.request('POST', '/product', data)
    }

    async updateProduct(id: number, data: Partial<ProductCreateRequest>): Promise<EnhancedAPIResponse<Product>> {
        return this.request('PUT', `/product/${id}`, data)
    }

    async deleteProduct(id: number): Promise<EnhancedAPIResponse<void>> {
        return this.request('DELETE', `/product/${id}`)
    }

    async getProductProjects(id: number): Promise<EnhancedAPIResponse<Project[]>> {
        return this.request('GET', `/product/${id}/projects`)
    }

    // ===== 项目相关API =====
    
    async getProjects(params?: ProjectEnhancedQuery): Promise<PaginatedResponse<Project>> {
        return this.request('GET', '/project', undefined, params)
    }

    async getProject(id: number): Promise<EnhancedAPIResponse<Project>> {
        return this.request('GET', `/project/${id}`)
    }

    async createProject(data: ProjectCreateRequest): Promise<EnhancedAPIResponse<Project>> {
        return this.request('POST', '/project', data)
    }

    async updateProject(id: number, data: Partial<ProjectCreateRequest>): Promise<EnhancedAPIResponse<Project>> {
        return this.request('PUT', `/project/${id}`, data)
    }

    async deleteProject(id: number): Promise<EnhancedAPIResponse<void>> {
        return this.request('DELETE', `/project/${id}`)
    }

    // ===== 微应用相关API =====
    
    async getMicroApps(params?: MicroAppEnhancedQuery): Promise<PaginatedResponse<MicroApp>> {
        return this.request('GET', '/app', undefined, params)
    }

    async getMicroApp(id: number): Promise<EnhancedAPIResponse<MicroApp>> {
        return this.request('GET', `/app/${id}`)
    }

    async createMicroApp(data: MicroAppCreateRequest): Promise<EnhancedAPIResponse<MicroApp>> {
        return this.request('POST', '/app', data)
    }

    async updateMicroApp(id: number, data: Partial<MicroAppCreateRequest>): Promise<EnhancedAPIResponse<MicroApp>> {
        return this.request('PUT', `/app/microapp/${id}`, data)
    }

    async deleteMicroApp(id: number): Promise<EnhancedAPIResponse<void>> {
        return this.request('DELETE', `/app/microapp/${id}`)
    }

    async favoriteMicroApp(id: number): Promise<EnhancedAPIResponse<void>> {
        return this.request('POST', `/app/microapp/${id}/favorite`)
    }

    async deployMicroApp(id: number, data: any): Promise<EnhancedAPIResponse<any>> {
        return this.request('POST', `/app/microapp/${id}/deploy`, data)
    }

    async getMicroAppHealth(id: number): Promise<EnhancedAPIResponse<any>> {
        return this.request('GET', `/app/microapp/${id}/health`)
    }

    async getMicroAppResources(id: number): Promise<EnhancedAPIResponse<any>> {
        return this.request('GET', `/app/microapp/${id}/resources`)
    }

    async getDeployHistory(id: number, params?: any): Promise<PaginatedResponse<any>> {
        return this.request('GET', `/app/microapp/${id}/deploy/history`, undefined, params)
    }

    // ===== 环境相关API =====
    
    async getEnvironments(params?: EnhancedListQuery): Promise<PaginatedResponse<Environment>> {
        return this.request('GET', '/environment', undefined, params)
    }

    async getEnvironment(id: number): Promise<EnhancedAPIResponse<Environment>> {
        return this.request('GET', `/environment/${id}`)
    }

    async createEnvironment(data: EnvironmentCreateRequest): Promise<EnhancedAPIResponse<Environment>> {
        return this.request('POST', '/environment', data)
    }

    async updateEnvironment(id: number, data: Partial<EnvironmentCreateRequest>): Promise<EnhancedAPIResponse<Environment>> {
        return this.request('PUT', `/environment/${id}`, data)
    }

    async deleteEnvironment(id: number): Promise<EnhancedAPIResponse<void>> {
        return this.request('DELETE', `/environment/${id}`)
    }

    // ===== 区域相关API =====
    
    async getRegions(params?: EnhancedListQuery): Promise<PaginatedResponse<Region>> {
        return this.request('GET', '/region', undefined, params)
    }

    // ===== 开发语言相关API =====
    
    async getDevLanguages(params?: EnhancedListQuery): Promise<PaginatedResponse<DevLanguage>> {
        return this.request('GET', '/app/language', undefined, params)
    }

    // ===== 数据中心相关API =====
    
    async getDataCenters(params?: EnhancedListQuery): Promise<PaginatedResponse<DataCenter>> {
        return this.request('GET', '/asset/datacenter', undefined, params)
    }

    // ===== 统计和分析API =====
    
    async getStatistics(): Promise<EnhancedAPIResponse<CMDBStatistics>> {
        return this.request('GET', '/statistics')
    }

    async getHealthStatus(): Promise<EnhancedAPIResponse<HealthStatus>> {
        return this.request('GET', '/health')
    }

    // ===== 批量操作API =====
    
    async batchDeleteProducts(ids: number[]): Promise<EnhancedAPIResponse<void>> {
        return this.request('DELETE', '/product/batch', { ids })
    }

    async batchDeleteProjects(ids: number[]): Promise<EnhancedAPIResponse<void>> {
        return this.request('DELETE', '/project/batch', { ids })
    }

    async batchDeleteMicroApps(ids: number[]): Promise<EnhancedAPIResponse<void>> {
        return this.request('DELETE', '/app/batch', { ids })
    }

    // ===== 导入导出API =====
    
    async exportProducts(params?: any): Promise<Blob> {
        const response = await request({
            method: 'get',
            url: `${this.baseURL}/product/export`,
            params,
            responseType: 'blob'
        })
        return response.data
    }

    async importProducts(file: File): Promise<EnhancedAPIResponse<any>> {
        const formData = new FormData()
        formData.append('file', file)
        return this.request('POST', '/product/import', formData)
    }
}

// 导出单例实例
export const enhancedCmdbApi = new EnhancedCMDBApi()

// 导出类型
export type {
    EnhancedAPIResponse,
    ErrorResponse,
    ValidationError,
    EnhancedListQuery,
    ProductEnhancedQuery,
    ProjectEnhancedQuery,
    MicroAppEnhancedQuery,
    CMDBStatistics,
    HealthStatus
}
