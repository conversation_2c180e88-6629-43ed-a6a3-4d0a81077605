import { http } from '@/utils/http'

// ============== 通用类型定义 ==============

export interface ApiResponse<T = any> {
  code: number
  data: T
  message?: string
}

export interface PaginationParams {
  page?: number
  page_size?: number
  search?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface PaginationResponse<T> {
  items: T[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// ============== Kubernetes 增强API ==============

export interface ClusterMetrics {
  node_count: number
  pod_count: number
  namespace_count: number
  service_count: number
  cpu_usage: string
  memory_usage: string
  storage_usage: string
  network_traffic: string
  health_status: string
  alerts: ClusterAlert[]
  resource_quotas: ResourceQuotaInfo[]
  top_pods: PodResourceUsage[]
  top_nodes: NodeResourceUsage[]
}

export interface ClusterAlert {
  level: string
  message: string
  component: string
  namespace: string
  timestamp: string
  resolved: boolean
}

export interface ResourceQuotaInfo {
  namespace: string
  name: string
  used: Record<string, string>
  hard: Record<string, string>
  usage: Record<string, string>
}

export interface PodResourceUsage {
  name: string
  namespace: string
  cpu_usage: string
  memory_usage: string
  node: string
}

export interface NodeResourceUsage {
  name: string
  cpu_usage: string
  memory_usage: string
  disk_usage: string
  pod_count: number
  status: string
}

// ============== 日志服务API ==============

export interface LogEntry {
  timestamp: string
  level: string
  message: string
  source: LogSource
  labels: Record<string, string>
  annotations: Record<string, string>
  raw: string
}

export interface LogSource {
  cluster: string
  namespace: string
  pod: string
  container: string
  node: string
}

export interface LogQuery {
  cluster_id?: number
  namespace?: string
  pod?: string
  container?: string
  start_time?: string
  end_time?: string
  level?: string
  keywords?: string[]
  labels?: Record<string, string>
  tail_lines?: number
  follow?: boolean
  previous?: boolean
  since_seconds?: number
}

export interface LogSearchResult {
  logs: LogEntry[]
  total: number
  statistics: LogStatistics
  query: LogQuery
  has_more: boolean
  next_cursor?: string
}

export interface LogStatistics {
  total_logs: number
  error_logs: number
  warn_logs: number
  info_logs: number
  debug_logs: number
  logs_by_level: Record<string, number>
  logs_by_source: Record<string, number>
  time_range: LogTimeRange
  top_keywords: KeywordCount[]
  log_trends: LogTrendPoint[]
}

export interface LogTimeRange {
  start_time: string
  end_time: string
  duration: string
}

export interface KeywordCount {
  keyword: string
  count: number
}

export interface LogTrendPoint {
  timestamp: string
  count: number
  level: string
}

// ============== CI/CD 增强API ==============

export interface PipelineMetrics {
  total_pipelines: number
  running_pipelines: number
  successful_builds: number
  failed_builds: number
  average_build_time: string
  success_rate: number
  build_trends: BuildTrendPoint[]
  top_failure_reasons: FailureReason[]
  resource_usage: PipelineResourceUsage
  quality_metrics: QualityMetrics
}

export interface BuildTrendPoint {
  date: string
  success_count: number
  failure_count: number
  total_count: number
  success_rate: number
}

export interface FailureReason {
  reason: string
  count: number
  rate: number
}

export interface PipelineResourceUsage {
  cpu_usage: string
  memory_usage: string
  disk_usage: string
  network_io: string
}

export interface QualityMetrics {
  code_coverage: number
  test_pass_rate: number
  security_issues: number
  code_smells: number
  vulnerabilities: number
  technical_debt: string
}

export interface DeploymentStatus {
  environment: string
  status: string
  version: string
  deploy_time: string
  health_status: string
  replicas: DeploymentReplicas
  resources: DeploymentResources
  services: ServiceStatus[]
  ingresses: IngressStatus[]
}

export interface DeploymentReplicas {
  desired: number
  current: number
  ready: number
  available: number
}

export interface DeploymentResources {
  cpu_request: string
  cpu_limit: string
  memory_request: string
  memory_limit: string
}

export interface ServiceStatus {
  name: string
  type: string
  cluster_ip: string
  ports: ServicePort[]
  endpoints: ServiceEndpoint[]
}

export interface ServicePort {
  name: string
  port: number
  target_port: string
  protocol: string
}

export interface ServiceEndpoint {
  ip: string
  port: number
}

export interface IngressStatus {
  name: string
  hosts: IngressHost[]
}

export interface IngressHost {
  host: string
  paths: IngressPath[]
}

export interface IngressPath {
  path: string
  path_type: string
  service_name: string
  service_port: number
}

// ============== 工作流增强API ==============

export interface WorkflowMetrics {
  total_workflows: number
  active_workflows: number
  completed_workflows: number
  pending_workflows: number
  average_completion_time: string
  completion_rate: number
  workflow_trends: WorkflowTrendPoint[]
  status_distribution: WorkflowStatusCount[]
  category_stats: WorkflowCategoryStats[]
  performance_metrics: WorkflowPerformance
}

export interface WorkflowTrendPoint {
  date: string
  created: number
  completed: number
  pending: number
}

export interface WorkflowStatusCount {
  status: string
  count: number
  rate: number
}

export interface WorkflowCategoryStats {
  category_id: number
  category_name: string
  count: number
  avg_completion_time: string
  success_rate: number
}

export interface WorkflowPerformance {
  fastest_completion: string
  slowest_completion: string
  median_completion_time: string
  p95_completion_time: string
  throughput_per_day: number
}

export interface WorkflowExecution {
  workflow_id: number
  current_step: number
  total_steps: number
  status: string
  progress: number
  start_time: string
  estimated_end?: string
  steps: WorkflowStepExecution[]
  variables: Record<string, any>
  logs: WorkflowLog[]
}

export interface WorkflowStepExecution {
  step_id: number
  name: string
  type: string
  status: string
  start_time?: string
  end_time?: string
  duration: string
  input: Record<string, any>
  output: Record<string, any>
  error: string
  retry_count: number
  max_retries: number
}

export interface WorkflowLog {
  timestamp: string
  level: string
  message: string
  step_id?: number
  data: Record<string, any>
}

// ============== 用户中心增强API ==============

export interface UserMetrics {
  total_users: number
  active_users: number
  new_users: number
  online_users: number
  user_growth_trend: UserGrowthPoint[]
  user_distribution: UserDistribution[]
  login_stats: UserLoginStats
  security_metrics: UserSecurityMetrics
  role_distribution: RoleDistribution[]
}

export interface UserGrowthPoint {
  date: string
  new_users: number
  total_users: number
}

export interface UserDistribution {
  category: string
  count: number
  rate: number
}

export interface UserLoginStats {
  total_logins: number
  successful_logins: number
  failed_logins: number
  success_rate: number
  avg_session_time: string
  peak_hours: number[]
}

export interface UserSecurityMetrics {
  password_expired: number
  mfa_enabled: number
  suspicious_logins: number
  locked_accounts: number
  security_alerts: number
  compliance_score: number
}

export interface RoleDistribution {
  role_id: number
  role_name: string
  count: number
  rate: number
}

export interface UserProfile {
  user_id: number
  username: string
  email: string
  full_name: string
  avatar: string
  department: string
  position: string
  phone: string
  status: string
  last_login?: string
  created_at: string
  roles: UserRole[]
  permissions: UserPermission[]
  organizations: UserOrganization[]
  security_settings: UserSecuritySettings
  preferences: Record<string, any>
  activity_summary: UserActivitySummary
}

export interface UserRole {
  role_id: number
  role_name: string
  description: string
  assigned_at: string
  assigned_by: string
}

export interface UserPermission {
  permission_id: number
  permission_name: string
  resource: string
  action: string
  source: string
}

export interface UserOrganization {
  org_id: number
  org_name: string
  role: string
  department: string
  joined_at: string
}

export interface UserSecuritySettings {
  mfa_enabled: boolean
  password_expiry?: string
  last_password_change: string
  login_attempts: number
  account_locked: boolean
  lock_expiry?: string
  trusted_devices: string[]
}

export interface UserActivitySummary {
  total_logins: number
  last_login?: string
  session_count: number
  avg_session_time: string
  recent_activities: UserActivity[]
}

export interface UserActivity {
  activity_id: number
  activity_type: string
  description: string
  ip_address: string
  user_agent: string
  timestamp: string
  status: string
}

// ============== API 客户端类 ==============

export class EnhancedPlatformApi {
  // Kubernetes 增强API
  static async getClusterMetrics(clusterId: number): Promise<ClusterMetrics> {
    const response = await http.get<ApiResponse<ClusterMetrics>>(`/api/v1/kubernetes/${clusterId}/metrics`)
    return response.data
  }

  static async getClusterHealth(clusterId: number): Promise<any> {
    const response = await http.get<ApiResponse<any>>(`/api/v1/kubernetes/${clusterId}/health`)
    return response.data
  }

  static async getClusterOverview(clusterId: number): Promise<any> {
    const response = await http.get<ApiResponse<any>>(`/api/v1/kubernetes/${clusterId}/overview`)
    return response.data
  }

  static async getResourceQuotas(clusterId: number, namespace?: string): Promise<any> {
    const params = namespace ? { namespace } : {}
    const response = await http.get<ApiResponse<any>>(`/api/v1/kubernetes/${clusterId}/quotas`, { params })
    return response.data
  }

  static async getTopResources(clusterId: number, type: 'pods' | 'nodes' = 'pods', limit: number = 10): Promise<any> {
    const response = await http.get<ApiResponse<any>>(`/api/v1/kubernetes/${clusterId}/top`, {
      params: { type, limit }
    })
    return response.data
  }

  static async getClusterAlerts(clusterId: number, level?: string, resolved: boolean = false): Promise<any> {
    const params: any = { resolved }
    if (level) params.level = level
    const response = await http.get<ApiResponse<any>>(`/api/v1/kubernetes/${clusterId}/alerts`, { params })
    return response.data
  }

  // 日志服务API
  static async getLogs(query: LogQuery): Promise<LogSearchResult> {
    const response = await http.get<ApiResponse<LogSearchResult>>('/api/v1/logs', { params: query })
    return response.data
  }

  static async getLogStatistics(query: LogQuery): Promise<LogStatistics> {
    const response = await http.get<ApiResponse<LogStatistics>>('/api/v1/logs/statistics', { params: query })
    return response.data
  }

  static async exportLogs(query: LogQuery, format: 'json' | 'csv' | 'txt' = 'json'): Promise<Blob> {
    const response = await http.get('/api/v1/logs/export', {
      params: { ...query, format },
      responseType: 'blob'
    })
    return response
  }

  // CI/CD 增强API
  static async getPipelineMetrics(timeRange: string = '7d'): Promise<PipelineMetrics> {
    const response = await http.get<ApiResponse<PipelineMetrics>>('/api/cicd/metrics', {
      params: { time_range: timeRange }
    })
    return response.data
  }

  static async getDeploymentStatus(appId: number, environment: string = 'production'): Promise<DeploymentStatus> {
    const response = await http.get<ApiResponse<DeploymentStatus>>(`/api/cicd/apps/${appId}/deployment/status`, {
      params: { environment }
    })
    return response.data
  }

  static async triggerPipeline(appId: number, branch: string, params?: Record<string, any>): Promise<any> {
    const response = await http.post<ApiResponse<any>>(`/api/cicd/apps/${appId}/pipeline/trigger`, {
      branch,
      params
    })
    return response.data
  }

  static async getBuildTrends(timeRange: string = '7d', appId?: number): Promise<any> {
    const queryParams: any = { time_range: timeRange }
    if (appId) queryParams.app_id = appId
    const response = await http.get<ApiResponse<any>>('/api/cicd/trends', { params: queryParams })
    return response.data
  }

  static async getFailureAnalysis(timeRange: string = '7d'): Promise<any> {
    const response = await http.get<ApiResponse<any>>('/api/cicd/failure-analysis', {
      params: { time_range: timeRange }
    })
    return response.data
  }

  static async getResourceUsage(): Promise<any> {
    const response = await http.get<ApiResponse<any>>('/api/cicd/resources')
    return response.data
  }

  static async getQualityMetrics(appId?: number): Promise<QualityMetrics> {
    const params = appId ? { app_id: appId } : {}
    const response = await http.get<ApiResponse<QualityMetrics>>('/api/cicd/quality', { params })
    return response.data
  }

  static async getPipelineOverview(): Promise<any> {
    const response = await http.get<ApiResponse<any>>('/api/cicd/overview')
    return response.data
  }

  // 工作流增强API
  static async getWorkflowMetrics(timeRange: string = '7d'): Promise<WorkflowMetrics> {
    const response = await http.get<ApiResponse<WorkflowMetrics>>('/api/v1/workflows/metrics', {
      params: { time_range: timeRange }
    })
    return response.data
  }

  static async executeWorkflow(templateId: number, variables: Record<string, any>): Promise<WorkflowExecution> {
    const response = await http.post<ApiResponse<WorkflowExecution>>(`/api/v1/workflows/templates/${templateId}/execute`, {
      variables
    })
    return response.data
  }

  static async getWorkflowTrends(timeRange: string = '7d'): Promise<any> {
    const response = await http.get<ApiResponse<any>>('/api/v1/workflows/trends', {
      params: { time_range: timeRange }
    })
    return response.data
  }

  static async getWorkflowStatusDistribution(timeRange: string = '7d'): Promise<any> {
    const response = await http.get<ApiResponse<any>>('/api/v1/workflows/status-distribution', {
      params: { time_range: timeRange }
    })
    return response.data
  }

  static async getWorkflowCategoryStats(timeRange: string = '7d'): Promise<WorkflowCategoryStats[]> {
    const response = await http.get<ApiResponse<WorkflowCategoryStats[]>>('/api/v1/workflows/category-stats', {
      params: { time_range: timeRange }
    })
    return response.data
  }

  static async getWorkflowPerformance(): Promise<WorkflowPerformance> {
    const response = await http.get<ApiResponse<WorkflowPerformance>>('/api/v1/workflows/performance')
    return response.data
  }

  static async getWorkflowOverview(): Promise<any> {
    const response = await http.get<ApiResponse<any>>('/api/v1/workflows/overview')
    return response.data
  }

  static async getWorkflowExecution(workflowId: number): Promise<WorkflowExecution> {
    const response = await http.get<ApiResponse<WorkflowExecution>>(`/api/v1/workflows/${workflowId}/execution`)
    return response.data
  }

  static async stopWorkflow(workflowId: number): Promise<any> {
    const response = await http.post<ApiResponse<any>>(`/api/v1/workflows/${workflowId}/stop`)
    return response.data
  }

  // 用户中心增强API
  static async getUserMetrics(timeRange: string = '7d'): Promise<UserMetrics> {
    const response = await http.get<ApiResponse<UserMetrics>>('/api/v1/users/metrics', {
      params: { time_range: timeRange }
    })
    return response.data
  }

  static async getUserProfile(userId: number): Promise<UserProfile> {
    const response = await http.get<ApiResponse<UserProfile>>(`/api/v1/users/${userId}/profile`)
    return response.data
  }

  static async getUserGrowthTrend(timeRange: string = '7d'): Promise<any> {
    const response = await http.get<ApiResponse<any>>('/api/v1/users/growth-trend', {
      params: { time_range: timeRange }
    })
    return response.data
  }

  static async getUserDistribution(): Promise<any> {
    const response = await http.get<ApiResponse<any>>('/api/v1/users/distribution')
    return response.data
  }

  static async getLoginStats(timeRange: string = '7d'): Promise<UserLoginStats> {
    const response = await http.get<ApiResponse<UserLoginStats>>('/api/v1/users/login-stats', {
      params: { time_range: timeRange }
    })
    return response.data
  }

  static async getSecurityMetrics(): Promise<UserSecurityMetrics> {
    const response = await http.get<ApiResponse<UserSecurityMetrics>>('/api/v1/users/security-metrics')
    return response.data
  }

  static async getUserOverview(): Promise<any> {
    const response = await http.get<ApiResponse<any>>('/api/v1/users/overview')
    return response.data
  }

  static async getUserActivity(userId: number, limit: number = 20): Promise<any> {
    const response = await http.get<ApiResponse<any>>(`/api/v1/users/${userId}/activity`, {
      params: { limit }
    })
    return response.data
  }

  static async getUserSessions(userId: number): Promise<any> {
    const response = await http.get<ApiResponse<any>>(`/api/v1/users/${userId}/sessions`)
    return response.data
  }

  static async revokeUserSession(userId: number, sessionId: string): Promise<any> {
    const response = await http.post<ApiResponse<any>>(`/api/v1/users/${userId}/sessions/${sessionId}/revoke`)
    return response.data
  }
}

export default EnhancedPlatformApi
