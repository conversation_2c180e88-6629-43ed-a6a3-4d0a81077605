<template>
  <div class="enhanced-product-view" :class="{ 'mobile-view': isMobile }">
    <!-- 移动端头部 -->
    <div class="mobile-header" v-if="isMobile">
      <div class="mobile-title">
        <h2>产品管理</h2>
        <div class="mobile-actions">
          <el-button type="primary" size="small" :icon="Plus" @click="showCreateDialog = true">
            新建
          </el-button>
          <el-dropdown @command="handleMobileAction">
            <el-button size="small" :icon="MoreFilled" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="export" :icon="Download">导出</el-dropdown-item>
                <el-dropdown-item command="import" :icon="Upload">导入</el-dropdown-item>
                <el-dropdown-item command="refresh" :icon="Refresh">刷新</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <div class="mobile-search">
        <el-input v-model="searchForm.search" placeholder="搜索产品" :prefix-icon="Search" clearable @input="handleSearch"
          size="small" />
      </div>
      <div class="mobile-filters" v-if="showMobileFilters">
        <el-select v-model="searchForm.region_id" placeholder="选择区域" clearable @change="handleSearch" size="small"
          style="width: 100%; margin-bottom: 8px;">
          <el-option v-for="region in regions" :key="region.id" :label="region.name" :value="region.id" />
        </el-select>
        <el-select v-model="searchForm.sort_field" placeholder="排序方式" @change="handleSearch" size="small"
          style="width: 100%;">
          <el-option label="创建时间" value="created_at" />
          <el-option label="更新时间" value="updated_at" />
          <el-option label="产品名称" value="name" />
          <el-option label="产品代码" value="product_code" />
        </el-select>
      </div>
      <div class="mobile-filter-toggle">
        <el-button size="small" text @click="showMobileFilters = !showMobileFilters"
          :icon="showMobileFilters ? ArrowUp : ArrowDown">
          {{ showMobileFilters ? '收起筛选' : '展开筛选' }}
        </el-button>
      </div>
    </div>

    <!-- 桌面端头部 -->
    <div class="desktop-header" v-else>
      <div class="page-header">
        <div class="header-left">
          <h1 class="page-title">产品管理</h1>
          <p class="page-description">管理和维护产品信息，支持批量操作和高级搜索</p>
        </div>
        <div class="header-right">
          <div class="header-stats">
            <div class="stat-item">
              <div class="stat-value">{{ totalProducts }}</div>
              <div class="stat-label">总产品数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ activeProducts }}</div>
              <div class="stat-label">活跃产品</div>
            </div>
          </div>
          <div class="header-actions">
            <el-button type="primary" @click="showCreateDialog = true" :icon="Plus">
              新建产品
            </el-button>
            <el-button @click="handleExport" :icon="Download">导出</el-button>
            <el-button @click="handleImport" :icon="Upload">导入</el-button>
            <el-button @click="handleRefresh" :loading="loading" :icon="Refresh">刷新</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 桌面端搜索和筛选 -->
    <div class="search-section" v-if="!isMobile">
      <el-card>
        <div class="search-form">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-input v-model="searchForm.search" placeholder="搜索产品名称或代码" :prefix-icon="Search" clearable
                @input="handleSearch" />
            </el-col>
            <el-col :span="4">
              <el-select v-model="searchForm.region_id" placeholder="选择区域" clearable @change="handleSearch">
                <el-option v-for="region in regions" :key="region.id" :label="region.name" :value="region.id" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="searchForm.sort_field" placeholder="排序字段" @change="handleSearch">
                <el-option label="创建时间" value="created_at" />
                <el-option label="更新时间" value="updated_at" />
                <el-option label="产品名称" value="name" />
                <el-option label="产品代码" value="product_code" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="searchForm.sort_order" placeholder="排序方向" @change="handleSearch">
                <el-option label="升序" value="ASC" />
                <el-option label="降序" value="DESC" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-button @click="resetSearch" :icon="Refresh">重置</el-button>
              <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section" v-if="!isMobile">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ totalProducts }}</div>
              <div class="stat-label">总产品数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ activeProducts }}</div>
              <div class="stat-label">活跃产品</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ regions.length }}</div>
              <div class="stat-label">总区域数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ selectedProducts.length }}</div>
              <div class="stat-label">已选择</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 批量操作栏 -->
    <div class="batch-actions" v-if="selectedProducts.length > 0">
      <el-alert :title="`已选择 ${selectedProducts.length} 个产品`" type="info" show-icon :closable="false">
        <template #default>
          <div class="batch-buttons">
            <el-button size="small" @click="handleBatchDelete" type="danger">
              批量删除
            </el-button>
            <el-button size="small" @click="handleBatchExport">
              批量导出
            </el-button>
            <el-button size="small" @click="clearSelection">
              清除选择
            </el-button>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 桌面端产品列表 -->
    <div class="table-section" v-if="!isMobile">
      <el-card>
        <el-table v-loading="loading" :data="products" @selection-change="handleSelectionChange"
          @sort-change="handleSortChange" stripe border>
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="ID" width="80" sortable="custom" />
          <el-table-column prop="product_code" label="产品代码" width="150" sortable="custom">
            <template #default="{ row }">
              <el-tag type="primary">{{ row.product_code }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="产品名称" min-width="200" sortable="custom">
            <template #default="{ row }">
              <div class="product-name">
                <strong>{{ row.name }}</strong>
                <p class="product-description" v-if="row.description">
                  {{ row.description }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="region" label="区域" width="120">
            <template #default="{ row }">
              <el-tag v-if="row.region" size="small">{{ row.region.name }}</el-tag>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="name_prefix" label="名称前缀" width="100">
            <template #default="{ row }">
              <el-tag v-if="row.name_prefix" size="small" type="info">
                {{ row.name_prefix }}
              </el-tag>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="180" sortable="custom">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" label="更新时间" width="180" sortable="custom">
            <template #default="{ row }">
              {{ formatDateTime(row.updated_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button size="small" @click="handleView(row)" :icon="View">
                查看
              </el-button>
              <el-button size="small" type="primary" @click="handleEdit(row)" :icon="Edit">
                编辑
              </el-button>
              <el-button size="small" type="danger" @click="handleDelete(row)" :icon="Delete">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 桌面端分页 -->
        <div class="pagination-wrapper">
          <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.page_size"
            :page-sizes="[10, 20, 50, 100]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange" @current-change="handlePageChange" />
        </div>
      </el-card>
    </div>

    <!-- 移动端产品列表 -->
    <div class="mobile-list-section" v-else>
      <div v-loading="loading" class="mobile-product-list">
        <div v-for="product in products" :key="product.id" class="mobile-product-card">
          <el-card shadow="hover" @click="handleView(product)">
            <div class="mobile-card-header">
              <div class="product-info">
                <h3 class="product-name">{{ product.name }}</h3>
                <el-tag type="primary" size="small">{{ product.product_code }}</el-tag>
              </div>
              <el-dropdown @command="(command) => handleMobileCardAction(command, product)">
                <el-button size="small" :icon="MoreFilled" circle />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="view" :icon="View">查看</el-dropdown-item>
                    <el-dropdown-item command="edit" :icon="Edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="delete" :icon="Delete">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <div class="mobile-card-content">
              <p v-if="product.description" class="product-description">
                {{ product.description }}
              </p>
              <div class="product-meta">
                <div class="meta-item" v-if="product.region">
                  <span class="meta-label">区域:</span>
                  <el-tag size="small">{{ product.region.name }}</el-tag>
                </div>
                <div class="meta-item" v-if="product.name_prefix">
                  <span class="meta-label">前缀:</span>
                  <el-tag size="small" type="info">{{ product.name_prefix }}</el-tag>
                </div>
                <div class="meta-item">
                  <span class="meta-label">创建时间:</span>
                  <span class="meta-value">{{ formatDateTime(product.created_at) }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 移动端分页 -->
      <div class="mobile-pagination">
        <el-pagination v-model:current-page="pagination.page" v-model:page-size="pagination.page_size"
          :page-sizes="[5, 10, 20]" :total="pagination.total" layout="prev, pager, next"
          @size-change="handlePageSizeChange" @current-change="handlePageChange" small />
      </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <ProductFormDialog v-model:visible="showCreateDialog" :product="currentProduct" :regions="regions"
      @success="handleFormSuccess" />

    <!-- 产品详情对话框 -->
    <ProductDetailDialog v-model:visible="showDetailDialog" :product="currentProduct" />

    <!-- 导入对话框 -->
    <ImportDialog v-model:visible="showImportDialog" @success="handleImportSuccess" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Download, Upload, Search, Refresh, View, Edit, Delete,
  MoreFilled, ArrowUp, ArrowDown
} from '@element-plus/icons-vue'
import { productApi, regionApi } from '@/api/modules/cmdb'
import type {
  Product, Region, ProductListQuery
} from '@/api/modules/cmdb'
import { formatDateTime } from '@/utils/date'

// 移动端检测
const isMobile = ref(false)
const showMobileFilters = ref(false)

// 响应式数据
const loading = ref(false)
const products = ref<Product[]>([])
const regions = ref<Region[]>([])
const selectedProducts = ref<Product[]>([])
const currentProduct = ref<Product | null>(null)

// 统计数据
const totalProducts = ref(0)
const activeProducts = ref(0)

// 对话框状态
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showImportDialog = ref(false)

// 搜索表单
const searchForm = reactive<ProductListQuery>({
  search: '',
  region_id: undefined,
  sort_by: 'created_at',
  sort_order: 'desc'
})

// 分页
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 计算属性
const hasSelection = computed(() => selectedProducts.value.length > 0)

// 移动端检测
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const handleResize = () => {
  checkMobile()
}

// 生命周期
onMounted(() => {
  checkMobile()
  window.addEventListener('resize', handleResize)
  loadData()
  loadRegions()
  loadStatistics()
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.page,
      page_size: pagination.page_size
    }

    const response = await productApi.getProducts(params)
    products.value = response.data.items || []
    pagination.total = response.data.total || 0
    totalProducts.value = response.data.total || 0
    // 简单计算活跃产品数（这里可以根据实际业务逻辑调整）
    activeProducts.value = Math.floor(totalProducts.value * 0.8)
  } catch (error: any) {
    ElMessage.error(error.message || '加载产品列表失败')
  } finally {
    loading.value = false
  }
}

const loadRegions = async () => {
  try {
    const response = await regionApi.getRegions()
    regions.value = response.data.items || []
  } catch (error: any) {
    console.error('加载区域列表失败:', error)
  }
}

const loadStatistics = async () => {
  // 这里可以添加统计信息的加载逻辑
  // 目前使用产品数据来计算基本统计
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    search: '',
    region_id: undefined,
    sort_by: 'created_at',
    sort_order: 'desc'
  })
  handleSearch()
}

// 移动端操作处理
const handleMobileAction = (command: string) => {
  switch (command) {
    case 'export':
      handleExport()
      break
    case 'import':
      handleImport()
      break
    case 'refresh':
      handleRefresh()
      break
  }
}

const handleRefresh = () => {
  loadData()
  loadRegions()
  loadStatistics()
}

// 移动端卡片操作处理
const handleMobileCardAction = (command: string, product: Product) => {
  switch (command) {
    case 'view':
      handleView(product)
      break
    case 'edit':
      handleEdit(product)
      break
    case 'delete':
      handleDelete(product)
      break
  }
}

const handleSelectionChange = (selection: Product[]) => {
  selectedProducts.value = selection
}

const clearSelection = () => {
  selectedProducts.value = []
}

const handleSortChange = ({ prop, order }: any) => {
  if (prop) {
    searchForm.sort_by = prop
    searchForm.sort_order = order === 'ascending' ? 'asc' : 'desc'
    handleSearch()
  }
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadData()
}

const handlePageSizeChange = (size: number) => {
  pagination.page_size = size
  pagination.page = 1
  loadData()
}

const handleView = (product: Product) => {
  currentProduct.value = product
  showDetailDialog.value = true
}

const handleEdit = (product: Product) => {
  currentProduct.value = product
  showCreateDialog.value = true
}

const handleDelete = async (product: Product) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除产品 "${product.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await productApi.deleteProduct(product.id!)
    ElMessage.success('删除成功')
    loadData()
    loadStatistics()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedProducts.value.length} 个产品吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedProducts.value.map(p => p.id!).filter(Boolean)
    // 批量删除需要逐个调用删除API
    await Promise.all(ids.map(id => productApi.deleteProduct(id)))
    ElMessage.success('批量删除成功')
    clearSelection()
    loadData()
    loadStatistics()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '批量删除失败')
    }
  }
}

const handleExport = async () => {
  try {
    // 简化导出功能，直接导出当前页面数据为JSON
    const dataStr = JSON.stringify(products.value, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `products_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error: any) {
    ElMessage.error(error.message || '导出失败')
  }
}

const handleBatchExport = async () => {
  try {
    // 导出选中的产品数据
    const selectedData = selectedProducts.value
    const dataStr = JSON.stringify(selectedData, null, 2)
    const blob = new Blob([dataStr], { type: 'application/json' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `selected_products_${new Date().getTime()}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('批量导出成功')
  } catch (error: any) {
    ElMessage.error(error.message || '批量导出失败')
  }
}

const handleImport = () => {
  showImportDialog.value = true
}

const handleFormSuccess = () => {
  showCreateDialog.value = false
  currentProduct.value = null
  loadData()
  loadStatistics()
}

const handleImportSuccess = () => {
  showImportDialog.value = false
  loadData()
  loadStatistics()
}
</script>

<style scoped>
.enhanced-product-view {
  padding: 20px;
}

.enhanced-product-view.mobile-view {
  padding: 12px;
}

/* 移动端头部样式 */
.mobile-header {
  margin-bottom: 16px;
}

.mobile-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.mobile-title h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.mobile-actions {
  display: flex;
  gap: 8px;
}

.mobile-search {
  margin-bottom: 8px;
}

.mobile-filters {
  margin-bottom: 8px;
}

.mobile-filter-toggle {
  text-align: center;
  margin-bottom: 16px;
}

/* 桌面端头部样式 */
.desktop-header {
  margin-bottom: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.search-section {
  margin-bottom: 20px;
}

.search-form {
  padding: 16px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.batch-actions {
  margin-bottom: 20px;
}

.batch-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.table-section {
  margin-bottom: 20px;
}

.product-name strong {
  display: block;
  margin-bottom: 4px;
}

.product-description {
  margin: 0;
  color: #909399;
  font-size: 12px;
  line-height: 1.4;
}

.text-muted {
  color: #c0c4cc;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
