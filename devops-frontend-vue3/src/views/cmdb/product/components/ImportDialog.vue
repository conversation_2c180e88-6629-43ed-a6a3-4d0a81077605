<template>
  <el-dialog
    v-model="dialogVisible"
    title="导入产品"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="import-content">
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" align-center class="import-steps">
        <el-step title="选择文件" />
        <el-step title="数据预览" />
        <el-step title="导入完成" />
      </el-steps>

      <!-- 步骤1: 文件选择 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="upload-section">
          <el-upload
            ref="uploadRef"
            class="upload-dragger"
            drag
            :auto-upload="false"
            :limit="1"
            accept=".xlsx,.xls,.csv"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持 .xlsx, .xls, .csv 格式文件，文件大小不超过 10MB
              </div>
            </template>
          </el-upload>
        </div>

        <div class="template-section">
          <el-divider>或者</el-divider>
          <div class="template-actions">
            <el-button @click="downloadTemplate" :icon="Download">
              下载导入模板
            </el-button>
            <span class="template-tip">
              建议先下载模板，按照模板格式填写数据后再导入
            </span>
          </div>
        </div>

        <div class="format-info">
          <el-alert
            title="导入格式说明"
            type="info"
            :closable="false"
            show-icon
          >
            <ul class="format-list">
              <li>产品代码：必填，字母开头，可包含字母、数字、下划线、连字符</li>
              <li>产品名称：必填，长度2-100字符</li>
              <li>区域代码：可选，需要是系统中已存在的区域代码</li>
              <li>名称前缀：可选，长度不超过20字符</li>
              <li>产品描述：可选，长度不超过500字符</li>
            </ul>
          </el-alert>
        </div>
      </div>

      <!-- 步骤2: 数据预览 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="preview-header">
          <div class="preview-info">
            <span>文件名：{{ selectedFile?.name }}</span>
            <span>数据行数：{{ previewData.length }}</span>
          </div>
          <el-button size="small" @click="currentStep = 0">重新选择</el-button>
        </div>

        <div class="preview-table" v-loading="parsing">
          <el-table
            :data="previewData.slice(0, 10)"
            border
            stripe
            max-height="300"
          >
            <el-table-column prop="product_code" label="产品代码" width="150" />
            <el-table-column prop="name" label="产品名称" min-width="200" />
            <el-table-column prop="region_code" label="区域代码" width="120" />
            <el-table-column prop="name_prefix" label="名称前缀" width="120" />
            <el-table-column prop="description" label="产品描述" min-width="200" show-overflow-tooltip />
          </el-table>
          
          <div v-if="previewData.length > 10" class="preview-tip">
            仅显示前10条数据，实际将导入 {{ previewData.length }} 条数据
          </div>
        </div>

        <div class="validation-result" v-if="validationErrors.length > 0">
          <el-alert
            title="数据验证失败"
            type="error"
            :closable="false"
            show-icon
          >
            <div class="error-list">
              <div
                v-for="(error, index) in validationErrors.slice(0, 5)"
                :key="index"
                class="error-item"
              >
                第{{ error.row }}行：{{ error.message }}
              </div>
              <div v-if="validationErrors.length > 5" class="error-more">
                还有 {{ validationErrors.length - 5 }} 个错误...
              </div>
            </div>
          </el-alert>
        </div>
      </div>

      <!-- 步骤3: 导入结果 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="result-content">
          <el-result
            :icon="importResult.success ? 'success' : 'error'"
            :title="importResult.success ? '导入成功' : '导入失败'"
            :sub-title="importResult.message"
          >
            <template #extra>
              <div class="result-stats" v-if="importResult.success">
                <div class="stat-item">
                  <div class="stat-number">{{ importResult.total || 0 }}</div>
                  <div class="stat-label">总数据</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ importResult.success_count || 0 }}</div>
                  <div class="stat-label">成功</div>
                </div>
                <div class="stat-item">
                  <div class="stat-number">{{ importResult.error_count || 0 }}</div>
                  <div class="stat-label">失败</div>
                </div>
              </div>
            </template>
          </el-result>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ currentStep === 2 ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="currentStep === 1"
          type="primary"
          @click="handleImport"
          :loading="importing"
          :disabled="validationErrors.length > 0"
        >
          开始导入
        </el-button>
        <el-button
          v-if="currentStep === 0"
          type="primary"
          @click="handleNext"
          :disabled="!selectedFile"
        >
          下一步
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Download } from '@element-plus/icons-vue'
import { enhancedCmdbApi } from '@/api/modules/enhanced-cmdb'

// Props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const currentStep = ref(0)
const selectedFile = ref<File | null>(null)
const previewData = ref<any[]>([])
const validationErrors = ref<any[]>([])
const parsing = ref(false)
const importing = ref(false)
const importResult = ref<any>({})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const handleFileChange = (file: any) => {
  selectedFile.value = file.raw
}

const handleFileRemove = () => {
  selectedFile.value = null
  previewData.value = []
  validationErrors.value = []
}

const downloadTemplate = () => {
  // 创建模板数据
  const templateData = [
    ['产品代码', '产品名称', '区域代码', '名称前缀', '产品描述'],
    ['web-platform', 'Web平台', 'cn-north', 'web', '企业级Web应用平台'],
    ['mobile-app', '移动应用', 'cn-south', 'app', '移动端应用服务']
  ]
  
  // 创建CSV内容
  const csvContent = templateData.map(row => row.join(',')).join('\n')
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  
  // 下载文件
  const link = document.createElement('a')
  link.href = URL.createObjectURL(blob)
  link.download = '产品导入模板.csv'
  link.click()
  URL.revokeObjectURL(link.href)
  
  ElMessage.success('模板下载成功')
}

const handleNext = async () => {
  if (!selectedFile.value) return
  
  try {
    parsing.value = true
    // 这里应该解析文件内容，暂时使用模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    previewData.value = [
      {
        product_code: 'test-product-1',
        name: '测试产品1',
        region_code: 'cn-north',
        name_prefix: 'test',
        description: '这是一个测试产品'
      },
      {
        product_code: 'test-product-2',
        name: '测试产品2',
        region_code: 'cn-south',
        name_prefix: 'test2',
        description: '这是另一个测试产品'
      }
    ]
    
    // 模拟验证错误
    validationErrors.value = []
    
    currentStep.value = 1
  } catch (error: any) {
    ElMessage.error('文件解析失败：' + error.message)
  } finally {
    parsing.value = false
  }
}

const handleImport = async () => {
  if (!selectedFile.value) return
  
  try {
    importing.value = true
    
    // 调用导入API
    const response = await enhancedCmdbApi.importProducts(selectedFile.value)
    
    importResult.value = {
      success: true,
      message: '数据导入成功',
      total: previewData.value.length,
      success_count: previewData.value.length,
      error_count: 0
    }
    
    currentStep.value = 2
    ElMessage.success('导入成功')
  } catch (error: any) {
    importResult.value = {
      success: false,
      message: error.message || '导入失败'
    }
    currentStep.value = 2
  } finally {
    importing.value = false
  }
}

const handleClose = () => {
  if (currentStep.value === 2 && importResult.value.success) {
    emit('success')
  }
  
  // 重置状态
  currentStep.value = 0
  selectedFile.value = null
  previewData.value = []
  validationErrors.value = []
  importResult.value = {}
  
  emit('update:visible', false)
}
</script>

<style scoped>
.import-content {
  min-height: 400px;
}

.import-steps {
  margin-bottom: 30px;
}

.step-content {
  padding: 20px 0;
}

.upload-section {
  margin-bottom: 20px;
}

.upload-dragger {
  width: 100%;
}

.template-section {
  margin-bottom: 20px;
}

.template-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: center;
}

.template-tip {
  font-size: 12px;
  color: #909399;
}

.format-info {
  margin-top: 20px;
}

.format-list {
  margin: 8px 0 0 0;
  padding-left: 20px;
}

.format-list li {
  margin-bottom: 4px;
  font-size: 13px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.preview-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #606266;
}

.preview-table {
  margin-bottom: 16px;
}

.preview-tip {
  text-align: center;
  color: #909399;
  font-size: 12px;
  margin-top: 8px;
}

.validation-result {
  margin-top: 16px;
}

.error-list {
  margin-top: 8px;
}

.error-item {
  font-size: 13px;
  margin-bottom: 4px;
}

.error-more {
  font-size: 13px;
  color: #909399;
  margin-top: 8px;
}

.result-content {
  text-align: center;
}

.result-stats {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-top: 20px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
