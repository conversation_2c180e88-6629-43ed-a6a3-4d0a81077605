<template>
  <div class="enhanced-dashboard">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="dashboard-title">DevOps 平台总览</h1>
          <p class="dashboard-subtitle">实时监控和管理您的DevOps基础设施</p>
        </div>
        <div class="header-actions">
          <el-select v-model="timeRange" @change="refreshData" size="small">
            <el-option label="最近1天" value="1d" />
            <el-option label="最近7天" value="7d" />
            <el-option label="最近30天" value="30d" />
          </el-select>
          <el-button @click="refreshData" :loading="loading" :icon="Refresh" size="small">
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 概览卡片 -->
    <div class="overview-cards" v-loading="loading">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="overview-card kubernetes">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="32"><Platform /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">Kubernetes</div>
                <div class="card-value">{{ kubernetesStats.clusters }}</div>
                <div class="card-subtitle">{{ kubernetesStats.nodes }} 节点</div>
              </div>
              <div class="card-status" :class="kubernetesStats.status">
                {{ getStatusText(kubernetesStats.status) }}
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card cicd">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="32"><VideoPlay /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">CI/CD</div>
                <div class="card-value">{{ cicdStats.pipelines }}</div>
                <div class="card-subtitle">{{ cicdStats.success_rate }}% 成功率</div>
              </div>
              <div class="card-status" :class="cicdStats.status">
                {{ getStatusText(cicdStats.status) }}
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card workflow">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="32"><Connection /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">工作流</div>
                <div class="card-value">{{ workflowStats.total }}</div>
                <div class="card-subtitle">{{ workflowStats.active }} 运行中</div>
              </div>
              <div class="card-status" :class="workflowStats.status">
                {{ getStatusText(workflowStats.status) }}
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card users">
            <div class="card-content">
              <div class="card-icon">
                <el-icon size="32"><User /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-title">用户</div>
                <div class="card-value">{{ userStats.total }}</div>
                <div class="card-subtitle">{{ userStats.online }} 在线</div>
              </div>
              <div class="card-status" :class="userStats.status">
                {{ getStatusText(userStats.status) }}
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <el-row :gutter="16">
        <!-- 左侧列 -->
        <el-col :span="16">
          <!-- 系统健康状态 -->
          <el-card class="content-card" shadow="never">
            <template #header>
              <div class="card-header">
                <span>系统健康状态</span>
                <el-button size="small" text @click="viewHealthDetails">
                  查看详情
                </el-button>
              </div>
            </template>
            <div class="health-overview">
              <el-row :gutter="16">
                <el-col :span="8">
                  <div class="health-item">
                    <div class="health-icon healthy">
                      <el-icon><CircleCheck /></el-icon>
                    </div>
                    <div class="health-info">
                      <div class="health-title">集群状态</div>
                      <div class="health-value">健康</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="health-item">
                    <div class="health-icon warning">
                      <el-icon><Warning /></el-icon>
                    </div>
                    <div class="health-info">
                      <div class="health-title">服务状态</div>
                      <div class="health-value">警告</div>
                    </div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="health-item">
                    <div class="health-icon healthy">
                      <el-icon><CircleCheck /></el-icon>
                    </div>
                    <div class="health-info">
                      <div class="health-title">存储状态</div>
                      <div class="health-value">正常</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>

          <!-- 资源使用趋势 -->
          <el-card class="content-card" shadow="never">
            <template #header>
              <div class="card-header">
                <span>资源使用趋势</span>
                <el-radio-group v-model="chartType" size="small">
                  <el-radio-button label="cpu">CPU</el-radio-button>
                  <el-radio-button label="memory">内存</el-radio-button>
                  <el-radio-button label="network">网络</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="chart-container">
              <div class="mock-chart">
                <div class="chart-title">{{ getChartTitle() }}</div>
                <div class="chart-data">
                  <div v-for="i in 12" :key="i" class="data-bar" 
                       :style="{ height: Math.random() * 80 + 20 + '%' }"></div>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 最近活动 -->
          <el-card class="content-card" shadow="never">
            <template #header>
              <div class="card-header">
                <span>最近活动</span>
                <el-button size="small" text @click="viewAllActivities">
                  查看全部
                </el-button>
              </div>
            </template>
            <div class="activity-list">
              <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
                <div class="activity-icon" :class="activity.type">
                  <el-icon>
                    <component :is="getActivityIcon(activity.type)" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-description">{{ activity.description }}</div>
                  <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
                </div>
                <div class="activity-status" :class="activity.status">
                  {{ activity.status }}
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧列 -->
        <el-col :span="8">
          <!-- 快速操作 -->
          <el-card class="content-card" shadow="never">
            <template #header>
              <span>快速操作</span>
            </template>
            <div class="quick-actions">
              <el-button type="primary" @click="createApplication" :icon="Plus" block>
                创建应用
              </el-button>
              <el-button @click="deployApplication" :icon="VideoPlay" block>
                部署应用
              </el-button>
              <el-button @click="viewLogs" :icon="Document" block>
                查看日志
              </el-button>
              <el-button @click="manageUsers" :icon="User" block>
                用户管理
              </el-button>
            </div>
          </el-card>

          <!-- 告警信息 -->
          <el-card class="content-card" shadow="never">
            <template #header>
              <div class="card-header">
                <span>告警信息</span>
                <el-badge :value="alerts.length" type="danger" />
              </div>
            </template>
            <div class="alerts-list">
              <div v-for="alert in alerts" :key="alert.id" class="alert-item" :class="alert.level">
                <div class="alert-icon">
                  <el-icon>
                    <component :is="getAlertIcon(alert.level)" />
                  </el-icon>
                </div>
                <div class="alert-content">
                  <div class="alert-title">{{ alert.title }}</div>
                  <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
                </div>
              </div>
              <div v-if="alerts.length === 0" class="no-alerts">
                <el-icon><CircleCheck /></el-icon>
                <span>暂无告警</span>
              </div>
            </div>
          </el-card>

          <!-- 系统信息 -->
          <el-card class="content-card" shadow="never">
            <template #header>
              <span>系统信息</span>
            </template>
            <div class="system-info">
              <div class="info-item">
                <span class="info-label">平台版本:</span>
                <span class="info-value">v2.1.0</span>
              </div>
              <div class="info-item">
                <span class="info-label">运行时间:</span>
                <span class="info-value">15天 8小时</span>
              </div>
              <div class="info-item">
                <span class="info-label">集群数量:</span>
                <span class="info-value">{{ kubernetesStats.clusters }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">应用数量:</span>
                <span class="info-value">{{ applicationCount }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">用户数量:</span>
                <span class="info-value">{{ userStats.total }}</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Refresh, Platform, VideoPlay, Connection, User, Plus, Document,
  CircleCheck, Warning, SuccessFilled, WarnTriangleFilled, CircleCloseFilled
} from '@element-plus/icons-vue'
import EnhancedPlatformApi from '@/api/modules/enhanced-platform'
import { formatTime } from '@/utils/date'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const timeRange = ref('7d')
const chartType = ref('cpu')
const applicationCount = ref(0)

// 统计数据
const kubernetesStats = reactive({
  clusters: 0,
  nodes: 0,
  status: 'healthy'
})

const cicdStats = reactive({
  pipelines: 0,
  success_rate: 0,
  status: 'healthy'
})

const workflowStats = reactive({
  total: 0,
  active: 0,
  status: 'healthy'
})

const userStats = reactive({
  total: 0,
  online: 0,
  status: 'healthy'
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    type: 'deploy',
    title: '应用部署',
    description: 'user-service 部署到生产环境',
    timestamp: new Date(Date.now() - 10 * 60 * 1000),
    status: 'success'
  },
  {
    id: 2,
    type: 'build',
    title: '构建完成',
    description: 'order-service 构建成功',
    timestamp: new Date(Date.now() - 30 * 60 * 1000),
    status: 'success'
  },
  {
    id: 3,
    type: 'alert',
    title: '告警触发',
    description: 'payment-service CPU使用率过高',
    timestamp: new Date(Date.now() - 45 * 60 * 1000),
    status: 'warning'
  }
])

// 告警信息
const alerts = ref([
  {
    id: 1,
    level: 'warning',
    title: 'CPU使用率过高',
    timestamp: new Date(Date.now() - 15 * 60 * 1000)
  },
  {
    id: 2,
    level: 'error',
    title: '服务连接失败',
    timestamp: new Date(Date.now() - 25 * 60 * 1000)
  }
])

// 生命周期
onMounted(() => {
  loadDashboardData()
})

// 方法
const loadDashboardData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadKubernetesStats(),
      loadCICDStats(),
      loadWorkflowStats(),
      loadUserStats()
    ])
  } catch (error: any) {
    ElMessage.error('加载仪表板数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const loadKubernetesStats = async () => {
  try {
    // 这里应该调用实际的API
    kubernetesStats.clusters = 3
    kubernetesStats.nodes = 12
    kubernetesStats.status = 'healthy'
  } catch (error) {
    console.error('加载Kubernetes统计失败:', error)
  }
}

const loadCICDStats = async () => {
  try {
    const metrics = await EnhancedPlatformApi.getPipelineMetrics(timeRange.value)
    cicdStats.pipelines = metrics.total_pipelines
    cicdStats.success_rate = Math.round(metrics.success_rate)
    cicdStats.status = metrics.success_rate > 90 ? 'healthy' : 'warning'
  } catch (error) {
    console.error('加载CI/CD统计失败:', error)
  }
}

const loadWorkflowStats = async () => {
  try {
    const metrics = await EnhancedPlatformApi.getWorkflowMetrics(timeRange.value)
    workflowStats.total = metrics.total_workflows
    workflowStats.active = metrics.active_workflows
    workflowStats.status = 'healthy'
  } catch (error) {
    console.error('加载工作流统计失败:', error)
  }
}

const loadUserStats = async () => {
  try {
    const metrics = await EnhancedPlatformApi.getUserMetrics(timeRange.value)
    userStats.total = metrics.total_users
    userStats.online = metrics.online_users
    userStats.status = 'healthy'
  } catch (error) {
    console.error('加载用户统计失败:', error)
  }
}

const refreshData = () => {
  loadDashboardData()
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    healthy: '健康',
    warning: '警告',
    error: '异常'
  }
  return statusMap[status] || '未知'
}

const getChartTitle = () => {
  const titleMap: Record<string, string> = {
    cpu: 'CPU使用率趋势',
    memory: '内存使用率趋势',
    network: '网络流量趋势'
  }
  return titleMap[chartType.value] || ''
}

const getActivityIcon = (type: string) => {
  const iconMap: Record<string, any> = {
    deploy: VideoPlay,
    build: Platform,
    alert: Warning,
    user: User
  }
  return iconMap[type] || CircleCheck
}

const getAlertIcon = (level: string) => {
  const iconMap: Record<string, any> = {
    error: CircleCloseFilled,
    warning: WarnTriangleFilled,
    info: SuccessFilled
  }
  return iconMap[level] || Warning
}

// 导航方法
const viewHealthDetails = () => {
  router.push('/kubernetes/cluster')
}

const viewAllActivities = () => {
  router.push('/system/audit')
}

const createApplication = () => {
  router.push('/microapp')
}

const deployApplication = () => {
  router.push('/cicd/pipeline')
}

const viewLogs = () => {
  router.push('/monitor/logs')
}

const manageUsers = () => {
  router.push('/system/user')
}
</script>

<style scoped>
.enhanced-dashboard {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.dashboard-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-section {
  flex: 1;
}

.dashboard-title {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.dashboard-subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
  border: none;
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.overview-card.kubernetes {
  border-left: 4px solid #409eff;
}

.overview-card.cicd {
  border-left: 4px solid #67c23a;
}

.overview-card.workflow {
  border-left: 4px solid #e6a23c;
}

.overview-card.users {
  border-left: 4px solid #f56c6c;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0;
}

.card-icon {
  margin-right: 16px;
  color: #409eff;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.card-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 2px;
}

.card-subtitle {
  font-size: 12px;
  color: #c0c4cc;
}

.card-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.card-status.healthy {
  background-color: #f0f9ff;
  color: #67c23a;
}

.card-status.warning {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.card-status.error {
  background-color: #fef0f0;
  color: #f56c6c;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.content-card {
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.health-overview {
  padding: 16px 0;
}

.health-item {
  display: flex;
  align-items: center;
  text-align: center;
}

.health-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 20px;
}

.health-icon.healthy {
  background-color: #f0f9ff;
  color: #67c23a;
}

.health-icon.warning {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.health-info {
  text-align: left;
}

.health-title {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.health-value {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.chart-container {
  height: 200px;
  padding: 16px 0;
}

.mock-chart {
  height: 100%;
  position: relative;
  background: linear-gradient(to bottom, #f5f7fa 0%, #ffffff 100%);
  border-radius: 6px;
  padding: 16px;
}

.chart-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 16px;
}

.chart-data {
  display: flex;
  align-items: end;
  height: 120px;
  gap: 8px;
}

.data-bar {
  flex: 1;
  background: linear-gradient(to top, #409eff, #79bbff);
  border-radius: 2px 2px 0 0;
  min-height: 10px;
  animation: chartAnimation 1s ease-in-out;
}

@keyframes chartAnimation {
  from { height: 0; }
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
}

.activity-icon.deploy {
  background-color: #f0f9ff;
  color: #67c23a;
}

.activity-icon.build {
  background-color: #f0f9ff;
  color: #409eff;
}

.activity-icon.alert {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.activity-description {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.activity-time {
  font-size: 11px;
  color: #c0c4cc;
}

.activity-status {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
}

.activity-status.success {
  background-color: #f0f9ff;
  color: #67c23a;
}

.activity-status.warning {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.alerts-list {
  max-height: 200px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-icon {
  margin-right: 8px;
  font-size: 16px;
}

.alert-item.warning .alert-icon {
  color: #e6a23c;
}

.alert-item.error .alert-icon {
  color: #f56c6c;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 13px;
  color: #303133;
  margin-bottom: 2px;
}

.alert-time {
  font-size: 11px;
  color: #c0c4cc;
}

.no-alerts {
  text-align: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;
}

.no-alerts .el-icon {
  margin-right: 4px;
  color: #67c23a;
}

.system-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.info-label {
  font-size: 13px;
  color: #909399;
}

.info-value {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-card__header) {
  padding: 16px 16px 0 16px;
}
</style>
