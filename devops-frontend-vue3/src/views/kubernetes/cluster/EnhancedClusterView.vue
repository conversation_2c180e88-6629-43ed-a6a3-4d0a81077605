<template>
  <div class="enhanced-cluster-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1>Kubernetes 集群管理</h1>
          <p>监控和管理您的Kubernetes集群</p>
        </div>
        <div class="header-actions">
          <el-select v-model="selectedCluster" @change="onClusterChange" placeholder="选择集群">
            <el-option
              v-for="cluster in clusters"
              :key="cluster.id"
              :label="cluster.name"
              :value="cluster.id"
            />
          </el-select>
          <el-button @click="refreshData" :loading="loading" :icon="Refresh">
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 集群概览 -->
    <div class="cluster-overview" v-if="selectedCluster" v-loading="loading">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-icon nodes">
                <el-icon><Platform /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ clusterMetrics.node_count }}</div>
                <div class="metric-label">节点数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-icon pods">
                <el-icon><Grid /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ clusterMetrics.pod_count }}</div>
                <div class="metric-label">Pod数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-icon namespaces">
                <el-icon><FolderOpened /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ clusterMetrics.namespace_count }}</div>
                <div class="metric-label">命名空间</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="metric-card">
            <div class="metric-content">
              <div class="metric-icon services">
                <el-icon><Connection /></el-icon>
              </div>
              <div class="metric-info">
                <div class="metric-value">{{ clusterMetrics.service_count }}</div>
                <div class="metric-label">服务数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容 -->
    <div class="main-content" v-if="selectedCluster">
      <el-row :gutter="16">
        <!-- 左侧列 -->
        <el-col :span="16">
          <!-- 资源使用情况 -->
          <el-card class="content-card">
            <template #header>
              <div class="card-header">
                <span>资源使用情况</span>
                <el-radio-group v-model="resourceType" size="small">
                  <el-radio-button label="cpu">CPU</el-radio-button>
                  <el-radio-button label="memory">内存</el-radio-button>
                  <el-radio-button label="storage">存储</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="resource-usage">
              <div class="usage-overview">
                <div class="usage-item">
                  <div class="usage-label">CPU使用率</div>
                  <el-progress :percentage="parseFloat(clusterMetrics.cpu_usage)" />
                  <div class="usage-value">{{ clusterMetrics.cpu_usage }}</div>
                </div>
                <div class="usage-item">
                  <div class="usage-label">内存使用率</div>
                  <el-progress :percentage="parseFloat(clusterMetrics.memory_usage)" />
                  <div class="usage-value">{{ clusterMetrics.memory_usage }}</div>
                </div>
                <div class="usage-item">
                  <div class="usage-label">存储使用率</div>
                  <el-progress :percentage="parseFloat(clusterMetrics.storage_usage)" />
                  <div class="usage-value">{{ clusterMetrics.storage_usage }}</div>
                </div>
              </div>
            </div>
          </el-card>

          <!-- Top资源使用 -->
          <el-card class="content-card">
            <template #header>
              <div class="card-header">
                <span>资源使用排行</span>
                <el-radio-group v-model="topResourceType" size="small" @change="loadTopResources">
                  <el-radio-button label="pods">Pod</el-radio-button>
                  <el-radio-button label="nodes">节点</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="top-resources">
              <el-table :data="topResources" stripe>
                <el-table-column prop="name" label="名称" />
                <el-table-column prop="namespace" label="命名空间" v-if="topResourceType === 'pods'" />
                <el-table-column prop="cpu_usage" label="CPU使用" />
                <el-table-column prop="memory_usage" label="内存使用" />
                <el-table-column prop="node" label="节点" v-if="topResourceType === 'pods'" />
                <el-table-column prop="status" label="状态" v-if="topResourceType === 'nodes'">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'Ready' ? 'success' : 'danger'" size="small">
                      {{ row.status }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>

          <!-- 资源配额 -->
          <el-card class="content-card">
            <template #header>
              <div class="card-header">
                <span>资源配额</span>
                <el-button size="small" text @click="viewAllQuotas">查看全部</el-button>
              </div>
            </template>
            <div class="resource-quotas">
              <div v-for="quota in resourceQuotas" :key="quota.name" class="quota-item">
                <div class="quota-header">
                  <span class="quota-name">{{ quota.name }}</span>
                  <el-tag size="small">{{ quota.namespace }}</el-tag>
                </div>
                <div class="quota-details">
                  <div v-for="(value, key) in quota.used" :key="key" class="quota-detail">
                    <span class="quota-resource">{{ key }}:</span>
                    <span class="quota-usage">{{ value }} / {{ quota.hard[key] }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧列 -->
        <el-col :span="8">
          <!-- 集群健康状态 -->
          <el-card class="content-card">
            <template #header>
              <div class="card-header">
                <span>集群健康状态</span>
                <el-tag :type="getHealthStatusType(clusterMetrics.health_status)" size="small">
                  {{ clusterMetrics.health_status }}
                </el-tag>
              </div>
            </template>
            <div class="health-status">
              <div class="health-item">
                <div class="health-icon healthy">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="health-info">
                  <div class="health-title">API Server</div>
                  <div class="health-value">正常</div>
                </div>
              </div>
              <div class="health-item">
                <div class="health-icon healthy">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="health-info">
                  <div class="health-title">etcd</div>
                  <div class="health-value">正常</div>
                </div>
              </div>
              <div class="health-item">
                <div class="health-icon warning">
                  <el-icon><Warning /></el-icon>
                </div>
                <div class="health-info">
                  <div class="health-title">Controller Manager</div>
                  <div class="health-value">警告</div>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 告警信息 -->
          <el-card class="content-card">
            <template #header>
              <div class="card-header">
                <span>告警信息</span>
                <el-badge :value="alerts.length" type="danger" />
              </div>
            </template>
            <div class="alerts-list">
              <div v-for="alert in alerts" :key="alert.timestamp" class="alert-item" :class="alert.level">
                <div class="alert-icon">
                  <el-icon>
                    <component :is="getAlertIcon(alert.level)" />
                  </el-icon>
                </div>
                <div class="alert-content">
                  <div class="alert-message">{{ alert.message }}</div>
                  <div class="alert-component">{{ alert.component }}</div>
                  <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
                </div>
              </div>
              <div v-if="alerts.length === 0" class="no-alerts">
                <el-icon><CircleCheck /></el-icon>
                <span>暂无告警</span>
              </div>
            </div>
          </el-card>

          <!-- 快速操作 -->
          <el-card class="content-card">
            <template #header>
              <span>快速操作</span>
            </template>
            <div class="quick-actions">
              <el-button @click="viewWorkloads" :icon="Grid" block>
                查看工作负载
              </el-button>
              <el-button @click="viewServices" :icon="Connection" block>
                查看服务
              </el-button>
              <el-button @click="viewConfigMaps" :icon="Document" block>
                查看配置
              </el-button>
              <el-button @click="viewEvents" :icon="Bell" block>
                查看事件
              </el-button>
            </div>
          </el-card>

          <!-- 集群信息 -->
          <el-card class="content-card">
            <template #header>
              <span>集群信息</span>
            </template>
            <div class="cluster-info">
              <div class="info-item">
                <span class="info-label">Kubernetes版本:</span>
                <span class="info-value">v1.28.2</span>
              </div>
              <div class="info-item">
                <span class="info-label">网络插件:</span>
                <span class="info-value">Calico</span>
              </div>
              <div class="info-item">
                <span class="info-label">存储类:</span>
                <span class="info-value">3个</span>
              </div>
              <div class="info-item">
                <span class="info-label">运行时间:</span>
                <span class="info-value">45天</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <el-empty description="请选择一个集群查看详情" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Refresh, Platform, Grid, FolderOpened, Connection, CircleCheck, Warning,
  Document, Bell, WarnTriangleFilled, CircleCloseFilled
} from '@element-plus/icons-vue'
import EnhancedPlatformApi, { type ClusterMetrics } from '@/api/modules/enhanced-platform'
import { formatTime } from '@/utils/date'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const selectedCluster = ref<number>()
const resourceType = ref('cpu')
const topResourceType = ref('pods')

// 集群列表
const clusters = ref([
  { id: 1, name: '生产集群' },
  { id: 2, name: '测试集群' },
  { id: 3, name: '开发集群' }
])

// 集群指标
const clusterMetrics = reactive<ClusterMetrics>({
  node_count: 0,
  pod_count: 0,
  namespace_count: 0,
  service_count: 0,
  cpu_usage: '0%',
  memory_usage: '0%',
  storage_usage: '0%',
  network_traffic: '0 MB/s',
  health_status: 'Unknown',
  alerts: [],
  resource_quotas: [],
  top_pods: [],
  top_nodes: []
})

// Top资源
const topResources = ref<any[]>([])

// 资源配额
const resourceQuotas = ref<any[]>([])

// 告警信息
const alerts = ref<any[]>([])

// 生命周期
onMounted(() => {
  if (clusters.value.length > 0) {
    selectedCluster.value = clusters.value[0].id
  }
})

// 监听集群变化
watch(selectedCluster, (newClusterId) => {
  if (newClusterId) {
    loadClusterData()
  }
}, { immediate: true })

// 方法
const loadClusterData = async () => {
  if (!selectedCluster.value) return

  loading.value = true
  try {
    await Promise.all([
      loadClusterMetrics(),
      loadTopResources(),
      loadResourceQuotas(),
      loadAlerts()
    ])
  } catch (error: any) {
    ElMessage.error('加载集群数据失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const loadClusterMetrics = async () => {
  try {
    const metrics = await EnhancedPlatformApi.getClusterMetrics(selectedCluster.value!)
    Object.assign(clusterMetrics, metrics)
  } catch (error) {
    console.error('加载集群指标失败:', error)
  }
}

const loadTopResources = async () => {
  try {
    const data = await EnhancedPlatformApi.getTopResources(selectedCluster.value!, topResourceType.value)
    topResources.value = data.resources || []
  } catch (error) {
    console.error('加载Top资源失败:', error)
  }
}

const loadResourceQuotas = async () => {
  try {
    const data = await EnhancedPlatformApi.getResourceQuotas(selectedCluster.value!)
    resourceQuotas.value = data.quotas || []
  } catch (error) {
    console.error('加载资源配额失败:', error)
  }
}

const loadAlerts = async () => {
  try {
    const data = await EnhancedPlatformApi.getClusterAlerts(selectedCluster.value!)
    alerts.value = data.alerts || []
  } catch (error) {
    console.error('加载告警信息失败:', error)
  }
}

const onClusterChange = () => {
  loadClusterData()
}

const refreshData = () => {
  loadClusterData()
}

const getHealthStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'Healthy': 'success',
    'Warning': 'warning',
    'Critical': 'danger'
  }
  return typeMap[status] || 'info'
}

const getAlertIcon = (level: string) => {
  const iconMap: Record<string, any> = {
    'Critical': CircleCloseFilled,
    'Warning': WarnTriangleFilled
  }
  return iconMap[level] || Warning
}

// 导航方法
const viewAllQuotas = () => {
  router.push(`/kubernetes/cluster/${selectedCluster.value}/quotas`)
}

const viewWorkloads = () => {
  router.push('/kubernetes/workload')
}

const viewServices = () => {
  router.push('/kubernetes/service')
}

const viewConfigMaps = () => {
  router.push('/kubernetes/configmap')
}

const viewEvents = () => {
  router.push('/kubernetes/events')
}
</script>

<style scoped>
.enhanced-cluster-view {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.title-section h1 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.title-section p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.cluster-overview {
  margin-bottom: 20px;
}

.metric-card {
  height: 100px;
  border: none;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
}

.metric-icon.nodes {
  background-color: #e6f7ff;
  color: #1890ff;
}

.metric-icon.pods {
  background-color: #f6ffed;
  color: #52c41a;
}

.metric-icon.namespaces {
  background-color: #fff7e6;
  color: #fa8c16;
}

.metric-icon.services {
  background-color: #fff1f0;
  color: #f5222d;
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: #909399;
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.content-card {
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.resource-usage {
  padding: 16px 0;
}

.usage-overview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.usage-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.usage-label {
  width: 80px;
  font-size: 14px;
  color: #606266;
}

.usage-value {
  width: 60px;
  text-align: right;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.top-resources {
  padding: 16px 0;
}

.resource-quotas {
  max-height: 300px;
  overflow-y: auto;
}

.quota-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.quota-item:last-child {
  border-bottom: none;
}

.quota-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.quota-name {
  font-weight: 500;
  color: #303133;
}

.quota-details {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.quota-detail {
  font-size: 12px;
  color: #606266;
}

.quota-resource {
  margin-right: 4px;
}

.quota-usage {
  font-weight: 500;
}

.health-status {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.health-item {
  display: flex;
  align-items: center;
}

.health-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 16px;
}

.health-icon.healthy {
  background-color: #f0f9ff;
  color: #67c23a;
}

.health-icon.warning {
  background-color: #fdf6ec;
  color: #e6a23c;
}

.health-info {
  flex: 1;
}

.health-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 2px;
}

.health-value {
  font-size: 12px;
  color: #909399;
}

.alerts-list {
  max-height: 200px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-icon {
  margin-right: 8px;
  margin-top: 2px;
  font-size: 16px;
}

.alert-item.Critical .alert-icon {
  color: #f56c6c;
}

.alert-item.Warning .alert-icon {
  color: #e6a23c;
}

.alert-content {
  flex: 1;
}

.alert-message {
  font-size: 13px;
  color: #303133;
  margin-bottom: 2px;
}

.alert-component {
  font-size: 11px;
  color: #909399;
  margin-bottom: 2px;
}

.alert-time {
  font-size: 11px;
  color: #c0c4cc;
}

.no-alerts {
  text-align: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;
}

.no-alerts .el-icon {
  margin-right: 4px;
  color: #67c23a;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.cluster-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.info-label {
  font-size: 13px;
  color: #909399;
}

.info-value {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-card__header) {
  padding: 16px 16px 0 16px;
}

:deep(.el-progress) {
  flex: 1;
}
</style>
