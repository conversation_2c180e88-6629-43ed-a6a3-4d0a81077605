<template>
  <div class="enhanced-microapp-view">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">微应用管理</h1>
        <p class="page-description">管理和监控微服务应用，支持部署、健康检查和资源监控</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="showCreateDialog = true" :icon="Plus">
          新建应用
        </el-button>
        <el-button @click="refreshData" :icon="Refresh" :loading="loading">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 快速统计 -->
    <div class="stats-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon color="#409eff"><Grid /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.total_apps || 0 }}</div>
                <div class="stat-label">总应用数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon color="#67c23a"><CircleCheck /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.running_apps || 0 }}</div>
                <div class="stat-label">运行中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon color="#e6a23c"><Warning /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ statistics.warning_apps || 0 }}</div>
                <div class="stat-label">异常</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">
                <el-icon color="#f56c6c"><Star /></el-icon>
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ favoriteApps.length }}</div>
                <div class="stat-label">收藏</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-card>
        <div class="search-form">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-input
                v-model="searchForm.search"
                placeholder="搜索应用名称或代码"
                :prefix-icon="Search"
                clearable
                @input="handleSearch"
              />
            </el-col>
            <el-col :span="4">
              <el-select
                v-model="searchForm.project_id"
                placeholder="选择项目"
                clearable
                @change="handleSearch"
              >
                <el-option
                  v-for="project in projects"
                  :key="project.id"
                  :label="project.name"
                  :value="project.id"
                />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select
                v-model="searchForm.language_code"
                placeholder="开发语言"
                clearable
                @change="handleSearch"
              >
                <el-option
                  v-for="lang in languages"
                  :key="lang.language_code"
                  :label="lang.name"
                  :value="lang.language_code"
                />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select
                v-model="searchForm.deployment_type"
                placeholder="部署类型"
                clearable
                @change="handleSearch"
              >
                <el-option label="Kubernetes" value="k8s" />
                <el-option label="Docker" value="docker" />
                <el-option label="虚拟机" value="vm" />
              </el-select>
            </el-col>
            <el-col :span="6">
              <el-button @click="resetSearch" :icon="Refresh">重置</el-button>
              <el-button type="primary" @click="handleSearch" :icon="Search">搜索</el-button>
              <el-button @click="toggleViewMode" :icon="viewMode === 'card' ? List : Grid">
                {{ viewMode === 'card' ? '列表' : '卡片' }}
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 应用列表 -->
    <div class="apps-section">
      <!-- 卡片视图 -->
      <div v-if="viewMode === 'card'" class="card-view">
        <el-row :gutter="16">
          <el-col
            v-for="app in microApps"
            :key="app.id"
            :xs="24" :sm="12" :md="8" :lg="6" :xl="4"
            class="app-card-col"
          >
            <MicroAppCard
              :app="app"
              @view="handleView"
              @edit="handleEdit"
              @delete="handleDelete"
              @deploy="handleDeploy"
              @favorite="handleFavorite"
              @health-check="handleHealthCheck"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 表格视图 -->
      <div v-else class="table-view">
        <el-card>
          <el-table
            v-loading="loading"
            :data="microApps"
            stripe
            border
            @sort-change="handleSortChange"
          >
            <el-table-column prop="id" label="ID" width="80" sortable="custom" />
            <el-table-column prop="app_code" label="应用代码" width="150" sortable="custom">
              <template #default="{ row }">
                <el-tag type="primary">{{ row.app_code }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="应用名称" min-width="200" sortable="custom">
              <template #default="{ row }">
                <div class="app-name-cell">
                  <div class="app-name">
                    <el-icon v-if="isFavorite(row.id)" color="#f56c6c"><Star /></el-icon>
                    <strong>{{ row.name }}</strong>
                  </div>
                  <div class="app-description" v-if="row.description">
                    {{ row.description }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="project" label="所属项目" width="150">
              <template #default="{ row }">
                <el-tag v-if="row.project" size="small">{{ row.project.name }}</el-tag>
                <span v-else class="text-muted">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="language_code" label="开发语言" width="120">
              <template #default="{ row }">
                <el-tag v-if="row.language_code" size="small" type="info">
                  {{ row.language_code }}
                </el-tag>
                <span v-else class="text-muted">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="deployment_type" label="部署类型" width="100">
              <template #default="{ row }">
                <el-tag
                  v-if="row.deployment_type"
                  size="small"
                  :type="getDeploymentTypeColor(row.deployment_type)"
                >
                  {{ getDeploymentTypeName(row.deployment_type) }}
                </el-tag>
                <span v-else class="text-muted">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="enabled" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.enabled ? 'success' : 'danger'" size="small">
                  {{ row.enabled ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_at" label="创建时间" width="180" sortable="custom">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="280" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="handleView(row)" :icon="View">
                  查看
                </el-button>
                <el-button size="small" type="primary" @click="handleEdit(row)" :icon="Edit">
                  编辑
                </el-button>
                <el-button size="small" type="success" @click="handleDeploy(row)" :icon="VideoPlay">
                  部署
                </el-button>
                <el-dropdown @command="(command) => handleDropdownCommand(command, row)">
                  <el-button size="small" :icon="More">
                    更多
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="favorite" :icon="Star">
                        {{ isFavorite(row.id) ? '取消收藏' : '收藏' }}
                      </el-dropdown-item>
                      <el-dropdown-item command="health" :icon="Monitor">
                        健康检查
                      </el-dropdown-item>
                      <el-dropdown-item command="resources" :icon="DataAnalysis">
                        资源监控
                      </el-dropdown-item>
                      <el-dropdown-item command="logs" :icon="Document">
                        查看日志
                      </el-dropdown-item>
                      <el-dropdown-item command="delete" :icon="Delete" divided>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="pagination.page"
              v-model:page-size="pagination.page_size"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handlePageSizeChange"
              @current-change="handlePageChange"
            />
          </div>
        </el-card>
      </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <MicroAppFormDialog
      v-model:visible="showCreateDialog"
      :app="currentApp"
      :projects="projects"
      :languages="languages"
      @success="handleFormSuccess"
    />

    <!-- 应用详情对话框 -->
    <MicroAppDetailDialog
      v-model:visible="showDetailDialog"
      :app="currentApp"
    />

    <!-- 部署对话框 -->
    <DeployDialog
      v-model:visible="showDeployDialog"
      :app="currentApp"
      @success="handleDeploySuccess"
    />

    <!-- 健康检查对话框 -->
    <HealthCheckDialog
      v-model:visible="showHealthDialog"
      :app="currentApp"
    />

    <!-- 资源监控对话框 -->
    <ResourceMonitorDialog
      v-model:visible="showResourceDialog"
      :app="currentApp"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Refresh, Search, Grid, List, CircleCheck, Warning, Star,
  View, Edit, VideoPlay, More, Monitor, DataAnalysis, Document, Delete
} from '@element-plus/icons-vue'
import { enhancedCmdbApi } from '@/api/modules/enhanced-cmdb'
import type { 
  MicroApp, Project, DevLanguage, MicroAppEnhancedQuery, CMDBStatistics 
} from '@/api/modules/enhanced-cmdb'
import { formatDateTime } from '@/utils/date'
import MicroAppCard from './components/MicroAppCard.vue'
import MicroAppFormDialog from './components/MicroAppFormDialog.vue'
import MicroAppDetailDialog from './components/MicroAppDetailDialog.vue'
import DeployDialog from './components/DeployDialog.vue'
import HealthCheckDialog from './components/HealthCheckDialog.vue'
import ResourceMonitorDialog from './components/ResourceMonitorDialog.vue'

// 响应式数据
const loading = ref(false)
const viewMode = ref<'card' | 'table'>('card')
const microApps = ref<MicroApp[]>([])
const projects = ref<Project[]>([])
const languages = ref<DevLanguage[]>([])
const statistics = ref<CMDBStatistics>({} as CMDBStatistics)
const favoriteApps = ref<number[]>([])
const currentApp = ref<MicroApp | null>(null)

// 对话框状态
const showCreateDialog = ref(false)
const showDetailDialog = ref(false)
const showDeployDialog = ref(false)
const showHealthDialog = ref(false)
const showResourceDialog = ref(false)

// 搜索表单
const searchForm = reactive<MicroAppEnhancedQuery>({
  search: '',
  project_id: undefined,
  language_code: '',
  deployment_type: '',
  sort_field: 'created_at',
  sort_order: 'DESC'
})

// 分页
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 生命周期
onMounted(() => {
  loadData()
  loadProjects()
  loadLanguages()
  loadStatistics()
  loadFavorites()
})

// 方法
const loadData = async () => {
  try {
    loading.value = true
    const params = {
      ...searchForm,
      page: pagination.page,
      page_size: pagination.page_size
    }
    
    const response = await enhancedCmdbApi.getMicroApps(params)
    microApps.value = response.data || []
    pagination.total = response.total || 0
  } catch (error: any) {
    ElMessage.error(error.message || '加载应用列表失败')
  } finally {
    loading.value = false
  }
}

const loadProjects = async () => {
  try {
    const response = await enhancedCmdbApi.getProjects()
    projects.value = response.data || []
  } catch (error: any) {
    console.error('加载项目列表失败:', error)
  }
}

const loadLanguages = async () => {
  try {
    const response = await enhancedCmdbApi.getDevLanguages()
    languages.value = response.data || []
  } catch (error: any) {
    console.error('加载开发语言列表失败:', error)
  }
}

const loadStatistics = async () => {
  try {
    const response = await enhancedCmdbApi.getStatistics()
    statistics.value = response.data || {}
  } catch (error: any) {
    console.error('加载统计信息失败:', error)
  }
}

const loadFavorites = () => {
  // 从本地存储加载收藏列表
  const favorites = localStorage.getItem('favorite_apps')
  if (favorites) {
    favoriteApps.value = JSON.parse(favorites)
  }
}

const saveFavorites = () => {
  localStorage.setItem('favorite_apps', JSON.stringify(favoriteApps.value))
}

const refreshData = () => {
  loadData()
  loadStatistics()
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    search: '',
    project_id: undefined,
    language_code: '',
    deployment_type: '',
    sort_field: 'created_at',
    sort_order: 'DESC'
  })
  handleSearch()
}

const toggleViewMode = () => {
  viewMode.value = viewMode.value === 'card' ? 'table' : 'card'
}

const handleSortChange = ({ prop, order }: any) => {
  if (prop) {
    searchForm.sort_field = prop
    searchForm.sort_order = order === 'ascending' ? 'ASC' : 'DESC'
    handleSearch()
  }
}

const handlePageChange = (page: number) => {
  pagination.page = page
  loadData()
}

const handlePageSizeChange = (size: number) => {
  pagination.page_size = size
  pagination.page = 1
  loadData()
}

const isFavorite = (appId: number) => {
  return favoriteApps.value.includes(appId)
}

const handleView = (app: MicroApp) => {
  currentApp.value = app
  showDetailDialog.value = true
}

const handleEdit = (app: MicroApp) => {
  currentApp.value = app
  showCreateDialog.value = true
}

const handleDelete = async (app: MicroApp) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除应用 "${app.name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await enhancedCmdbApi.deleteMicroApp(app.id!)
    ElMessage.success('删除成功')
    loadData()
    loadStatistics()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '删除失败')
    }
  }
}

const handleDeploy = (app: MicroApp) => {
  currentApp.value = app
  showDeployDialog.value = true
}

const handleFavorite = async (app: MicroApp) => {
  try {
    const index = favoriteApps.value.indexOf(app.id!)
    if (index > -1) {
      favoriteApps.value.splice(index, 1)
      ElMessage.success('已取消收藏')
    } else {
      favoriteApps.value.push(app.id!)
      ElMessage.success('已添加到收藏')
    }
    saveFavorites()
    
    // 调用后端API
    await enhancedCmdbApi.favoriteMicroApp(app.id!)
  } catch (error: any) {
    console.error('收藏操作失败:', error)
  }
}

const handleHealthCheck = (app: MicroApp) => {
  currentApp.value = app
  showHealthDialog.value = true
}

const handleDropdownCommand = (command: string, app: MicroApp) => {
  switch (command) {
    case 'favorite':
      handleFavorite(app)
      break
    case 'health':
      handleHealthCheck(app)
      break
    case 'resources':
      currentApp.value = app
      showResourceDialog.value = true
      break
    case 'logs':
      // 跳转到日志页面
      window.open(`/logs/${app.id}`, '_blank')
      break
    case 'delete':
      handleDelete(app)
      break
  }
}

const handleFormSuccess = () => {
  showCreateDialog.value = false
  currentApp.value = null
  loadData()
  loadStatistics()
}

const handleDeploySuccess = () => {
  showDeployDialog.value = false
  loadData()
}

const getDeploymentTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    k8s: 'primary',
    docker: 'success',
    vm: 'warning'
  }
  return colors[type] || 'info'
}

const getDeploymentTypeName = (type: string) => {
  const names: Record<string, string> = {
    k8s: 'K8s',
    docker: 'Docker',
    vm: 'VM'
  }
  return names[type] || type
}
</script>

<style scoped>
.enhanced-microapp-view {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.stats-section {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 16px;
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  color: #909399;
  font-size: 14px;
}

.search-section {
  margin-bottom: 20px;
}

.search-form {
  padding: 16px;
}

.apps-section {
  margin-bottom: 20px;
}

.card-view {
  min-height: 400px;
}

.app-card-col {
  margin-bottom: 16px;
}

.table-view {
  min-height: 400px;
}

.app-name-cell {
  line-height: 1.4;
}

.app-name {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

.app-description {
  font-size: 12px;
  color: #909399;
}

.text-muted {
  color: #c0c4cc;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
