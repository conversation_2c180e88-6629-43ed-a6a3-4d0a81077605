<template>
  <el-dialog
    v-model="dialogVisible"
    title="健康检查"
    width="700px"
    :close-on-click-modal="false"
  >
    <div class="health-check-content">
      <el-alert
        :title="`正在检查应用: ${app?.name}`"
        type="info"
        :closable="false"
        show-icon
        class="check-alert"
      />

      <!-- 检查进度 -->
      <div v-if="checking" class="check-progress">
        <el-progress
          :percentage="progress"
          :status="progressStatus"
          :stroke-width="8"
        />
        <div class="progress-text">{{ progressText }}</div>
      </div>

      <!-- 检查结果 -->
      <div v-if="healthData" class="health-results">
        <!-- 总体状态 -->
        <el-card class="status-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Monitor /></el-icon>
              <span>总体状态</span>
            </div>
          </template>
          
          <div class="overall-status">
            <div class="status-icon">
              <el-icon :color="getStatusColor(healthData.overall_status)" size="48">
                <component :is="getStatusIcon(healthData.overall_status)" />
              </el-icon>
            </div>
            <div class="status-info">
              <div class="status-text">{{ getStatusText(healthData.overall_status) }}</div>
              <div class="status-time">检查时间: {{ formatDateTime(healthData.check_time) }}</div>
            </div>
          </div>
        </el-card>

        <!-- 详细检查项 -->
        <el-card class="checks-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><List /></el-icon>
              <span>检查详情</span>
            </div>
          </template>
          
          <div class="check-items">
            <div
              v-for="check in healthData.checks"
              :key="check.name"
              class="check-item"
            >
              <div class="check-header">
                <div class="check-name">
                  <el-icon :color="getStatusColor(check.status)">
                    <component :is="getStatusIcon(check.status)" />
                  </el-icon>
                  <span>{{ check.name }}</span>
                </div>
                <el-tag :type="getStatusTagType(check.status)" size="small">
                  {{ getStatusText(check.status) }}
                </el-tag>
              </div>
              <div class="check-details" v-if="check.details">
                <div class="detail-item" v-for="(value, key) in check.details" :key="key">
                  <span class="detail-key">{{ key }}:</span>
                  <span class="detail-value">{{ value }}</span>
                </div>
              </div>
              <div class="check-message" v-if="check.message">
                {{ check.message }}
              </div>
            </div>
          </div>
        </el-card>

        <!-- 性能指标 -->
        <el-card class="metrics-card" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><DataAnalysis /></el-icon>
              <span>性能指标</span>
            </div>
          </template>
          
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="metric-item">
                <div class="metric-value" :class="getMetricClass(healthData.metrics.response_time)">
                  {{ healthData.metrics.response_time }}ms
                </div>
                <div class="metric-label">响应时间</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-item">
                <div class="metric-value" :class="getMetricClass(healthData.metrics.cpu_usage, 'percentage')">
                  {{ healthData.metrics.cpu_usage }}%
                </div>
                <div class="metric-label">CPU使用率</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-item">
                <div class="metric-value" :class="getMetricClass(healthData.metrics.memory_usage, 'percentage')">
                  {{ healthData.metrics.memory_usage }}%
                </div>
                <div class="metric-label">内存使用率</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-item">
                <div class="metric-value">{{ healthData.metrics.active_connections }}</div>
                <div class="metric-label">活跃连接</div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>

      <!-- 错误信息 -->
      <div v-if="error" class="error-info">
        <el-alert
          title="健康检查失败"
          :description="error"
          type="error"
          show-icon
          :closable="false"
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleCheck" :loading="checking">
          {{ healthData ? '重新检查' : '开始检查' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  Monitor, List, DataAnalysis, CircleCheck, Warning, CircleClose 
} from '@element-plus/icons-vue'
import { enhancedCmdbApi } from '@/api/modules/enhanced-cmdb'
import type { MicroApp } from '@/api/modules/enhanced-cmdb'
import { formatDateTime } from '@/utils/date'

// Props
interface Props {
  visible: boolean
  app?: MicroApp | null
}

const props = withDefaults(defineProps<Props>(), {
  app: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const checking = ref(false)
const progress = ref(0)
const progressText = ref('')
const progressStatus = ref<'success' | 'exception' | undefined>()
const healthData = ref<any>(null)
const error = ref('')

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听应用变化
watch(() => props.app, (newApp) => {
  if (newApp) {
    // 重置状态
    healthData.value = null
    error.value = ''
    // 自动开始检查
    handleCheck()
  }
})

// 方法
const handleCheck = async () => {
  if (!props.app) return
  
  try {
    checking.value = true
    progress.value = 0
    progressText.value = '开始健康检查...'
    progressStatus.value = undefined
    error.value = ''
    
    // 模拟检查过程
    await simulateHealthCheck()
    
  } catch (err: any) {
    error.value = err.message || '健康检查失败'
    progressStatus.value = 'exception'
  } finally {
    checking.value = false
  }
}

const simulateHealthCheck = async () => {
  const steps = [
    { text: '检查服务可用性...', progress: 20 },
    { text: '检查数据库连接...', progress: 40 },
    { text: '检查外部依赖...', progress: 60 },
    { text: '检查性能指标...', progress: 80 },
    { text: '生成检查报告...', progress: 100 }
  ]
  
  for (const step of steps) {
    progressText.value = step.text
    progress.value = step.progress
    await new Promise(resolve => setTimeout(resolve, 800))
  }
  
  // 模拟检查结果
  const statuses = ['healthy', 'warning', 'error']
  const overallStatus = statuses[Math.floor(Math.random() * statuses.length)]
  
  healthData.value = {
    overall_status: overallStatus,
    check_time: new Date(),
    checks: [
      {
        name: '服务可用性',
        status: 'healthy',
        details: {
          '端口': '8080',
          '协议': 'HTTP',
          '状态码': '200'
        },
        message: '服务正常运行'
      },
      {
        name: '数据库连接',
        status: overallStatus === 'error' ? 'error' : 'healthy',
        details: {
          '连接池': '10/20',
          '响应时间': '< 100ms'
        },
        message: overallStatus === 'error' ? '数据库连接异常' : '数据库连接正常'
      },
      {
        name: '外部依赖',
        status: overallStatus === 'warning' ? 'warning' : 'healthy',
        details: {
          'Redis': '正常',
          'MQ': overallStatus === 'warning' ? '延迟较高' : '正常'
        },
        message: overallStatus === 'warning' ? '部分依赖响应较慢' : '所有依赖正常'
      }
    ],
    metrics: {
      response_time: Math.floor(Math.random() * 500) + 50,
      cpu_usage: Math.floor(Math.random() * 80) + 10,
      memory_usage: Math.floor(Math.random() * 70) + 20,
      active_connections: Math.floor(Math.random() * 100) + 10
    }
  }
  
  progressStatus.value = overallStatus === 'healthy' ? 'success' : 'exception'
  progressText.value = '健康检查完成'
  
  // 调用后端API
  try {
    await enhancedCmdbApi.getMicroAppHealth(props.app!.id!)
  } catch (error) {
    console.error('调用健康检查API失败:', error)
  }
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    healthy: '#67c23a',
    warning: '#e6a23c',
    error: '#f56c6c'
  }
  return colors[status] || '#909399'
}

const getStatusIcon = (status: string) => {
  const icons: Record<string, any> = {
    healthy: CircleCheck,
    warning: Warning,
    error: CircleClose
  }
  return icons[status] || CircleCheck
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    healthy: '健康',
    warning: '警告',
    error: '异常'
  }
  return texts[status] || '未知'
}

const getStatusTagType = (status: string) => {
  const types: Record<string, string> = {
    healthy: 'success',
    warning: 'warning',
    error: 'danger'
  }
  return types[status] || 'info'
}

const getMetricClass = (value: number, type: string = 'time') => {
  if (type === 'percentage') {
    if (value > 80) return 'metric-danger'
    if (value > 60) return 'metric-warning'
    return 'metric-success'
  } else {
    if (value > 1000) return 'metric-danger'
    if (value > 500) return 'metric-warning'
    return 'metric-success'
  }
}

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.health-check-content {
  max-height: 70vh;
  overflow-y: auto;
}

.check-alert {
  margin-bottom: 20px;
}

.check-progress {
  margin-bottom: 20px;
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  color: #606266;
  font-size: 14px;
}

.health-results {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-card,
.checks-card,
.metrics-card {
  border: 1px solid #e4e7ed;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.overall-status {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
}

.status-icon {
  flex-shrink: 0;
}

.status-info {
  flex: 1;
}

.status-text {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.status-time {
  font-size: 12px;
  color: #909399;
}

.check-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.check-item {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.check-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.check-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.check-details {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 8px;
}

.detail-item {
  font-size: 12px;
}

.detail-key {
  color: #909399;
  margin-right: 4px;
}

.detail-value {
  color: #303133;
  font-weight: 500;
}

.check-message {
  font-size: 13px;
  color: #606266;
}

.metric-item {
  text-align: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.metric-value {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 4px;
}

.metric-value.metric-success {
  color: #67c23a;
}

.metric-value.metric-warning {
  color: #e6a23c;
}

.metric-value.metric-danger {
  color: #f56c6c;
}

.metric-label {
  font-size: 12px;
  color: #909399;
}

.error-info {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
