<template>
  <el-card class="micro-app-card" :class="{ favorite: isFavorite }" shadow="hover">
    <!-- 卡片头部 -->
    <template #header>
      <div class="card-header">
        <div class="app-info">
          <div class="app-name">
            <el-icon v-if="isFavorite" class="favorite-icon" color="#f56c6c">
              <Star />
            </el-icon>
            <span class="name-text">{{ app.name }}</span>
          </div>
          <div class="app-code">
            <el-tag size="small" type="primary">{{ app.app_code }}</el-tag>
          </div>
        </div>
        <div class="card-actions">
          <el-button
            size="small"
            circle
            :icon="isFavorite ? StarFilled : Star"
            :type="isFavorite ? 'danger' : 'default'"
            @click="handleFavorite"
          />
          <el-dropdown @command="handleCommand" trigger="click">
            <el-button size="small" circle :icon="More" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="view" :icon="View">查看详情</el-dropdown-item>
                <el-dropdown-item command="edit" :icon="Edit">编辑</el-dropdown-item>
                <el-dropdown-item command="deploy" :icon="VideoPlay">部署</el-dropdown-item>
                <el-dropdown-item command="health" :icon="Monitor" divided>健康检查</el-dropdown-item>
                <el-dropdown-item command="logs" :icon="Document">查看日志</el-dropdown-item>
                <el-dropdown-item command="delete" :icon="Delete" divided>删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </template>

    <!-- 卡片内容 -->
    <div class="card-content">
      <!-- 应用描述 -->
      <div class="app-description" v-if="app.description">
        <p>{{ app.description }}</p>
      </div>
      <div class="no-description" v-else>
        <span class="text-muted">暂无描述</span>
      </div>

      <!-- 应用标签 -->
      <div class="app-tags">
        <el-tag v-if="app.project" size="small" class="tag-item">
          <el-icon><FolderOpened /></el-icon>
          {{ app.project.name }}
        </el-tag>
        <el-tag v-if="app.language_code" size="small" type="info" class="tag-item">
          <el-icon><Code /></el-icon>
          {{ app.language_code }}
        </el-tag>
        <el-tag v-if="app.deployment_type" size="small" :type="getDeploymentTypeColor(app.deployment_type)" class="tag-item">
          <el-icon><Platform /></el-icon>
          {{ getDeploymentTypeName(app.deployment_type) }}
        </el-tag>
        <el-tag v-if="app.category_name" size="small" type="warning" class="tag-item">
          <el-icon><Collection /></el-icon>
          {{ app.category_name }}
        </el-tag>
      </div>

      <!-- 应用状态 -->
      <div class="app-status">
        <div class="status-item">
          <span class="status-label">状态:</span>
          <el-tag :type="app.enabled ? 'success' : 'danger'" size="small">
            {{ app.enabled ? '启用' : '禁用' }}
          </el-tag>
        </div>
        <div class="status-item" v-if="healthStatus">
          <span class="status-label">健康:</span>
          <el-tag :type="getHealthStatusColor(healthStatus)" size="small">
            {{ getHealthStatusText(healthStatus) }}
          </el-tag>
        </div>
      </div>

      <!-- 应用指标 -->
      <div class="app-metrics" v-if="metrics">
        <div class="metric-item">
          <div class="metric-value">{{ metrics.cpu || '0%' }}</div>
          <div class="metric-label">CPU</div>
        </div>
        <div class="metric-item">
          <div class="metric-value">{{ metrics.memory || '0MB' }}</div>
          <div class="metric-label">内存</div>
        </div>
        <div class="metric-item">
          <div class="metric-value">{{ metrics.instances || 0 }}</div>
          <div class="metric-label">实例</div>
        </div>
      </div>

      <!-- 最近部署信息 -->
      <div class="deploy-info" v-if="lastDeploy">
        <div class="deploy-item">
          <el-icon><Clock /></el-icon>
          <span class="deploy-text">
            最近部署: {{ formatDateTime(lastDeploy.created_at) }}
          </span>
        </div>
        <div class="deploy-item">
          <el-icon><User /></el-icon>
          <span class="deploy-text">
            部署人: {{ lastDeploy.deployer || '系统' }}
          </span>
        </div>
      </div>
    </div>

    <!-- 卡片底部操作 -->
    <template #footer>
      <div class="card-footer">
        <el-button size="small" @click="handleView" :icon="View">
          查看
        </el-button>
        <el-button size="small" type="primary" @click="handleEdit" :icon="Edit">
          编辑
        </el-button>
        <el-button size="small" type="success" @click="handleDeploy" :icon="VideoPlay">
          部署
        </el-button>
        <el-button size="small" @click="handleHealthCheck" :icon="Monitor">
          检查
        </el-button>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  Star, StarFilled, More, View, Edit, VideoPlay, Monitor, Document, Delete,
  FolderOpened, Code, Platform, Collection, Clock, User
} from '@element-plus/icons-vue'
import type { MicroApp } from '@/api/modules/enhanced-cmdb'
import { formatDateTime } from '@/utils/date'

// Props
interface Props {
  app: MicroApp
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  view: [app: MicroApp]
  edit: [app: MicroApp]
  delete: [app: MicroApp]
  deploy: [app: MicroApp]
  favorite: [app: MicroApp]
  'health-check': [app: MicroApp]
}>()

// 响应式数据
const healthStatus = ref<string>('')
const metrics = ref<any>(null)
const lastDeploy = ref<any>(null)

// 计算属性
const isFavorite = computed(() => {
  const favorites = localStorage.getItem('favorite_apps')
  if (favorites) {
    const favoriteIds = JSON.parse(favorites)
    return favoriteIds.includes(props.app.id)
  }
  return false
})

// 生命周期
onMounted(() => {
  loadHealthStatus()
  loadMetrics()
  loadLastDeploy()
})

// 方法
const loadHealthStatus = async () => {
  try {
    // 模拟健康状态检查
    const statuses = ['healthy', 'warning', 'error']
    healthStatus.value = statuses[Math.floor(Math.random() * statuses.length)]
  } catch (error) {
    console.error('加载健康状态失败:', error)
  }
}

const loadMetrics = async () => {
  try {
    // 模拟指标数据
    metrics.value = {
      cpu: `${Math.floor(Math.random() * 100)}%`,
      memory: `${Math.floor(Math.random() * 1000)}MB`,
      instances: Math.floor(Math.random() * 5) + 1
    }
  } catch (error) {
    console.error('加载指标数据失败:', error)
  }
}

const loadLastDeploy = async () => {
  try {
    // 模拟最近部署信息
    if (Math.random() > 0.3) {
      lastDeploy.value = {
        created_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
        deployer: '开发者' + Math.floor(Math.random() * 5 + 1)
      }
    }
  } catch (error) {
    console.error('加载部署信息失败:', error)
  }
}

const getDeploymentTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    k8s: 'primary',
    docker: 'success',
    vm: 'warning'
  }
  return colors[type] || 'info'
}

const getDeploymentTypeName = (type: string) => {
  const names: Record<string, string> = {
    k8s: 'K8s',
    docker: 'Docker',
    vm: 'VM'
  }
  return names[type] || type
}

const getHealthStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    healthy: 'success',
    warning: 'warning',
    error: 'danger'
  }
  return colors[status] || 'info'
}

const getHealthStatusText = (status: string) => {
  const texts: Record<string, string> = {
    healthy: '健康',
    warning: '警告',
    error: '异常'
  }
  return texts[status] || '未知'
}

const handleView = () => {
  emit('view', props.app)
}

const handleEdit = () => {
  emit('edit', props.app)
}

const handleDelete = () => {
  emit('delete', props.app)
}

const handleDeploy = () => {
  emit('deploy', props.app)
}

const handleFavorite = () => {
  emit('favorite', props.app)
}

const handleHealthCheck = () => {
  emit('health-check', props.app)
}

const handleCommand = (command: string) => {
  switch (command) {
    case 'view':
      handleView()
      break
    case 'edit':
      handleEdit()
      break
    case 'deploy':
      handleDeploy()
      break
    case 'health':
      handleHealthCheck()
      break
    case 'logs':
      window.open(`/logs/${props.app.id}`, '_blank')
      break
    case 'delete':
      handleDelete()
      break
  }
}
</script>

<style scoped>
.micro-app-card {
  height: 100%;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
}

.micro-app-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.micro-app-card.favorite {
  border-color: #f56c6c;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 0;
}

.app-info {
  flex: 1;
  min-width: 0;
}

.app-name {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
}

.favorite-icon {
  flex-shrink: 0;
}

.name-text {
  font-weight: 600;
  font-size: 16px;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.app-code {
  margin-bottom: 4px;
}

.card-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.card-content {
  padding: 0;
}

.app-description {
  margin-bottom: 16px;
}

.app-description p {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.no-description {
  margin-bottom: 16px;
  text-align: center;
  padding: 8px 0;
}

.text-muted {
  color: #c0c4cc;
  font-size: 12px;
}

.app-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 16px;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.app-status {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-label {
  font-size: 12px;
  color: #909399;
}

.app-metrics {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.metric-item {
  text-align: center;
}

.metric-value {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 2px;
}

.metric-label {
  font-size: 12px;
  color: #909399;
}

.deploy-info {
  margin-bottom: 16px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.deploy-item {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

.deploy-item:last-child {
  margin-bottom: 0;
}

.deploy-text {
  font-size: 12px;
  color: #606266;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  padding: 0;
}

.card-footer .el-button {
  flex: 1;
}

:deep(.el-card__header) {
  padding: 16px 16px 0 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-card__footer) {
  padding: 0 16px 16px 16px;
}
</style>
