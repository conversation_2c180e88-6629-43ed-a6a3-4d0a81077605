<template>
  <el-dialog
    v-model="dialogVisible"
    title="微应用详情"
    width="900px"
    :close-on-click-modal="false"
  >
    <div v-if="app" class="app-detail">
      <!-- 基本信息 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><InfoFilled /></el-icon>
            <span>基本信息</span>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="应用ID">
            <el-tag type="info">{{ app.id }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="应用代码">
            <el-tag type="primary">{{ app.app_code }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="应用名称">
            <strong>{{ app.name }}</strong>
          </el-descriptions-item>
          <el-descriptions-item label="所属项目">
            <el-tag v-if="app.project" size="small">
              {{ app.project.name }}
            </el-tag>
            <span v-else class="text-muted">未设置</span>
          </el-descriptions-item>
          <el-descriptions-item label="开发语言">
            <el-tag v-if="app.language_code" size="small" type="info">
              {{ app.language_code }}
            </el-tag>
            <span v-else class="text-muted">未设置</span>
          </el-descriptions-item>
          <el-descriptions-item label="应用分类">
            <el-tag v-if="app.category_name" size="small" type="warning">
              {{ app.category_name }}
            </el-tag>
            <span v-else class="text-muted">未设置</span>
          </el-descriptions-item>
          <el-descriptions-item label="部署类型">
            <el-tag v-if="app.deployment_type" size="small" :type="getDeploymentTypeColor(app.deployment_type)">
              {{ getDeploymentTypeName(app.deployment_type) }}
            </el-tag>
            <span v-else class="text-muted">未设置</span>
          </el-descriptions-item>
          <el-descriptions-item label="应用状态">
            <el-tag :type="app.enabled ? 'success' : 'danger'" size="small">
              {{ app.enabled ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(app.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(app.updated_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="应用描述" :span="2">
            <div v-if="app.description" class="description-text">
              {{ app.description }}
            </div>
            <span v-else class="text-muted">暂无描述</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 构建配置 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <div class="card-header">
            <el-icon><Tools /></el-icon>
            <span>构建配置</span>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="构建命令">
            <code v-if="app.build_command" class="build-command">{{ app.build_command }}</code>
            <span v-else class="text-muted">未设置</span>
          </el-descriptions-item>
          <el-descriptions-item label="扫描分支">
            <el-tag v-if="app.scan_branch" size="small" type="success">
              {{ app.scan_branch }}
            </el-tag>
            <span v-else class="text-muted">未设置</span>
          </el-descriptions-item>
          <el-descriptions-item label="多应用模式">
            <el-tag :type="app.is_multi_app ? 'success' : 'info'" size="small">
              {{ app.is_multi_app ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="流水线ID">
            <el-tag v-if="app.pipeline_id" size="small" type="primary">
              {{ app.pipeline_id }}
            </el-tag>
            <span v-else class="text-muted">未设置</span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 运行状态 -->
      <el-card class="detail-card" shadow="never" v-loading="statusLoading">
        <template #header>
          <div class="card-header">
            <el-icon><Monitor /></el-icon>
            <span>运行状态</span>
            <el-button size="small" @click="loadStatus" :loading="statusLoading">
              刷新
            </el-button>
          </div>
        </template>
        
        <div v-if="appStatus">
          <el-row :gutter="16">
            <el-col :span="8">
              <div class="status-item">
                <div class="status-label">健康状态</div>
                <el-tag :type="getHealthStatusColor(appStatus.health)" size="large">
                  {{ getHealthStatusText(appStatus.health) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="status-item">
                <div class="status-label">运行实例</div>
                <div class="status-value">{{ appStatus.instances || 0 }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="status-item">
                <div class="status-label">最后部署</div>
                <div class="status-value">{{ formatDateTime(appStatus.last_deploy) }}</div>
              </div>
            </el-col>
          </el-row>
          
          <el-divider />
          
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="metric-item">
                <div class="metric-value">{{ appStatus.cpu || '0%' }}</div>
                <div class="metric-label">CPU使用率</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-item">
                <div class="metric-value">{{ appStatus.memory || '0MB' }}</div>
                <div class="metric-label">内存使用</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-item">
                <div class="metric-value">{{ appStatus.requests || '0' }}</div>
                <div class="metric-label">请求数/分钟</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="metric-item">
                <div class="metric-value">{{ appStatus.errors || '0' }}</div>
                <div class="metric-label">错误数/分钟</div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div v-else class="no-data">
          <el-empty description="暂无运行状态数据" :image-size="60" />
        </div>
      </el-card>

      <!-- 部署历史 -->
      <el-card class="detail-card" shadow="never" v-loading="historyLoading">
        <template #header>
          <div class="card-header">
            <el-icon><Clock /></el-icon>
            <span>部署历史</span>
            <el-button size="small" @click="loadDeployHistory" :loading="historyLoading">
              刷新
            </el-button>
          </div>
        </template>
        
        <div v-if="deployHistory.length > 0">
          <el-timeline>
            <el-timeline-item
              v-for="deploy in deployHistory"
              :key="deploy.id"
              :timestamp="formatDateTime(deploy.created_at)"
              placement="top"
            >
              <div class="deploy-item">
                <div class="deploy-header">
                  <el-tag :type="getDeployStatusColor(deploy.status)" size="small">
                    {{ getDeployStatusText(deploy.status) }}
                  </el-tag>
                  <span class="deploy-version">{{ deploy.version || 'v1.0.0' }}</span>
                </div>
                <div class="deploy-info">
                  <span>部署人: {{ deploy.deployer || '系统' }}</span>
                  <span>环境: {{ deploy.environment || '生产环境' }}</span>
                </div>
                <div class="deploy-description" v-if="deploy.description">
                  {{ deploy.description }}
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
        <div v-else class="no-data">
          <el-empty description="暂无部署历史" :image-size="60" />
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑应用</el-button>
        <el-button type="success" @click="handleDeploy">部署应用</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  InfoFilled, Tools, Monitor, Clock 
} from '@element-plus/icons-vue'
import { enhancedCmdbApi } from '@/api/modules/enhanced-cmdb'
import type { MicroApp } from '@/api/modules/enhanced-cmdb'
import { formatDateTime } from '@/utils/date'

// Props
interface Props {
  visible: boolean
  app?: MicroApp | null
}

const props = withDefaults(defineProps<Props>(), {
  app: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  edit: [app: MicroApp]
  deploy: [app: MicroApp]
}>()

// 响应式数据
const appStatus = ref<any>(null)
const deployHistory = ref<any[]>([])
const statusLoading = ref(false)
const historyLoading = ref(false)

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听应用变化
watch(() => props.app, (newApp) => {
  if (newApp) {
    loadStatus()
    loadDeployHistory()
  }
}, { immediate: true })

// 方法
const loadStatus = async () => {
  if (!props.app?.id) return
  
  try {
    statusLoading.value = true
    // 模拟状态数据
    appStatus.value = {
      health: 'healthy',
      instances: Math.floor(Math.random() * 5) + 1,
      last_deploy: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      cpu: `${Math.floor(Math.random() * 100)}%`,
      memory: `${Math.floor(Math.random() * 1000)}MB`,
      requests: Math.floor(Math.random() * 1000),
      errors: Math.floor(Math.random() * 10)
    }
  } catch (error) {
    console.error('加载应用状态失败:', error)
  } finally {
    statusLoading.value = false
  }
}

const loadDeployHistory = async () => {
  if (!props.app?.id) return
  
  try {
    historyLoading.value = true
    // 模拟部署历史数据
    deployHistory.value = Array.from({ length: 5 }, (_, i) => ({
      id: i + 1,
      status: ['success', 'failed', 'running'][Math.floor(Math.random() * 3)],
      version: `v1.${i + 1}.0`,
      deployer: `开发者${i + 1}`,
      environment: '生产环境',
      description: `部署版本 v1.${i + 1}.0`,
      created_at: new Date(Date.now() - i * 24 * 60 * 60 * 1000)
    }))
  } catch (error) {
    console.error('加载部署历史失败:', error)
  } finally {
    historyLoading.value = false
  }
}

const getDeploymentTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    k8s: 'primary',
    docker: 'success',
    vm: 'warning'
  }
  return colors[type] || 'info'
}

const getDeploymentTypeName = (type: string) => {
  const names: Record<string, string> = {
    k8s: 'Kubernetes',
    docker: 'Docker',
    vm: '虚拟机'
  }
  return names[type] || type
}

const getHealthStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    healthy: 'success',
    warning: 'warning',
    error: 'danger'
  }
  return colors[status] || 'info'
}

const getHealthStatusText = (status: string) => {
  const texts: Record<string, string> = {
    healthy: '健康',
    warning: '警告',
    error: '异常'
  }
  return texts[status] || '未知'
}

const getDeployStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    success: 'success',
    failed: 'danger',
    running: 'warning'
  }
  return colors[status] || 'info'
}

const getDeployStatusText = (status: string) => {
  const texts: Record<string, string> = {
    success: '成功',
    failed: '失败',
    running: '进行中'
  }
  return texts[status] || '未知'
}

const handleEdit = () => {
  if (props.app) {
    emit('edit', props.app)
  }
}

const handleDeploy = () => {
  if (props.app) {
    emit('deploy', props.app)
  }
}
</script>

<style scoped>
.app-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 16px;
  border: 1px solid #e4e7ed;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.card-header .el-button {
  margin-left: auto;
}

.description-text {
  line-height: 1.6;
  color: #606266;
}

.text-muted {
  color: #c0c4cc;
}

.build-command {
  background-color: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.status-item {
  text-align: center;
  padding: 16px;
}

.status-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.status-value {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.metric-item {
  text-align: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.metric-value {
  font-size: 20px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 12px;
  color: #909399;
}

.deploy-item {
  padding: 8px 0;
}

.deploy-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.deploy-version {
  font-weight: 500;
  color: #303133;
}

.deploy-info {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.deploy-description {
  font-size: 13px;
  color: #606266;
}

.no-data {
  text-align: center;
  padding: 40px 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
}
</style>
