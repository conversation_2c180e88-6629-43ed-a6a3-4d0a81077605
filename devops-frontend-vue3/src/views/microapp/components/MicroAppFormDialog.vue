<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? '编辑微应用' : '新建微应用'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      @submit.prevent
    >
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="应用代码" prop="app_code">
            <el-input
              v-model="formData.app_code"
              placeholder="请输入应用代码"
              :disabled="isEdit"
              maxlength="250"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="应用名称" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入应用名称"
              maxlength="128"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="所属项目" prop="project_id">
            <el-select
              v-model="formData.project_id"
              placeholder="请选择所属项目"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="project in projects"
                :key="project.id"
                :label="project.name"
                :value="project.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开发语言" prop="language_code">
            <el-select
              v-model="formData.language_code"
              placeholder="请选择开发语言"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="lang in languages"
                :key="lang.language_code"
                :label="lang.name"
                :value="lang.language_code"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="应用分类" prop="category_name">
            <el-select
              v-model="formData.category_name"
              placeholder="请选择应用分类"
              clearable
              style="width: 100%"
            >
              <el-option label="前端应用" value="frontend" />
              <el-option label="后端服务" value="backend" />
              <el-option label="中间件" value="middleware" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="部署类型" prop="deployment_type">
            <el-select
              v-model="formData.deployment_type"
              placeholder="请选择部署类型"
              clearable
              style="width: 100%"
            >
              <el-option label="Kubernetes" value="k8s" />
              <el-option label="Docker" value="docker" />
              <el-option label="虚拟机" value="vm" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="构建命令" prop="build_command">
            <el-input
              v-model="formData.build_command"
              placeholder="请输入构建命令"
              maxlength="250"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="扫描分支" prop="scan_branch">
            <el-input
              v-model="formData.scan_branch"
              placeholder="请输入扫描分支"
              maxlength="64"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="应用描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="4"
          placeholder="请输入应用描述（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="应用状态">
        <el-switch
          v-model="formData.enabled"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>

      <el-form-item label="多应用模式">
        <el-switch
          v-model="formData.is_multi_app"
          active-text="是"
          inactive-text="否"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { enhancedCmdbApi } from '@/api/modules/enhanced-cmdb'
import type { MicroApp, Project, DevLanguage, MicroAppCreateRequest } from '@/api/modules/enhanced-cmdb'

// Props
interface Props {
  visible: boolean
  app?: MicroApp | null
  projects: Project[]
  languages: DevLanguage[]
}

const props = withDefaults(defineProps<Props>(), {
  app: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const formData = reactive<MicroAppCreateRequest>({
  app_code: '',
  name: '',
  project_id: undefined,
  language_code: '',
  category_name: '',
  deployment_type: '',
  build_command: '',
  scan_branch: 'main',
  description: '',
  enabled: true,
  is_multi_app: false,
  repo_settings: {},
  target_settings: {},
  team_members: {},
  template_data: {},
  multi_app_ids: {},
  docker_settings: {},
  notify_settings: {},
  edit_permission: {},
  module_settings: {}
})

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const isEdit = computed(() => !!props.app?.id)

// 表单验证规则
const formRules: FormRules = {
  app_code: [
    { required: true, message: '请输入应用代码', trigger: 'blur' },
    { 
      pattern: /^[a-zA-Z][a-zA-Z0-9_-]{1,249}$/, 
      message: '应用代码格式不正确', 
      trigger: 'blur' 
    }
  ],
  name: [
    { required: true, message: '请输入应用名称', trigger: 'blur' },
    { min: 2, max: 128, message: '应用名称长度应在2-128字符之间', trigger: 'blur' }
  ],
  build_command: [
    { max: 250, message: '构建命令长度不能超过250字符', trigger: 'blur' }
  ],
  scan_branch: [
    { max: 64, message: '扫描分支长度不能超过64字符', trigger: 'blur' }
  ],
  description: [
    { max: 500, message: '描述长度不能超过500字符', trigger: 'blur' }
  ]
}

// 监听应用变化
watch(() => props.app, (newApp) => {
  if (newApp) {
    Object.assign(formData, {
      app_code: newApp.app_code,
      name: newApp.name,
      project_id: newApp.project_id,
      language_code: newApp.language_code || '',
      category_name: newApp.category_name || '',
      deployment_type: newApp.deployment_type || '',
      build_command: newApp.build_command || '',
      scan_branch: newApp.scan_branch || 'main',
      description: newApp.description || '',
      enabled: newApp.enabled ?? true,
      is_multi_app: newApp.is_multi_app ?? false,
      repo_settings: newApp.repo_settings || {},
      target_settings: newApp.target_settings || {},
      team_members: newApp.team_members || {},
      template_data: newApp.template_data || {},
      multi_app_ids: newApp.multi_app_ids || {},
      docker_settings: newApp.docker_settings || {},
      notify_settings: newApp.notify_settings || {},
      edit_permission: newApp.edit_permission || {},
      module_settings: newApp.module_settings || {}
    })
  } else {
    resetForm()
  }
}, { immediate: true })

// 方法
const resetForm = () => {
  Object.assign(formData, {
    app_code: '',
    name: '',
    project_id: undefined,
    language_code: '',
    category_name: '',
    deployment_type: '',
    build_command: '',
    scan_branch: 'main',
    description: '',
    enabled: true,
    is_multi_app: false,
    repo_settings: {},
    target_settings: {},
    team_members: {},
    template_data: {},
    multi_app_ids: {},
    docker_settings: {},
    notify_settings: {},
    edit_permission: {},
    module_settings: {}
  })
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    submitting.value = true
    
    if (isEdit.value) {
      await enhancedCmdbApi.updateMicroApp(props.app!.id!, formData)
      ElMessage.success('微应用更新成功')
    } else {
      await enhancedCmdbApi.createMicroApp(formData)
      ElMessage.success('微应用创建成功')
    }
    
    emit('success')
  } catch (error: any) {
    ElMessage.error(error.message || '操作失败')
  } finally {
    submitting.value = false
  }
}

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
