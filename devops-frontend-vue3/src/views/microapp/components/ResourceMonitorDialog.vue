<template>
  <el-dialog
    v-model="dialogVisible"
    title="资源监控"
    width="900px"
    :close-on-click-modal="false"
  >
    <div class="resource-monitor-content">
      <el-alert
        :title="`监控应用: ${app?.name}`"
        type="info"
        :closable="false"
        show-icon
        class="monitor-alert"
      />

      <!-- 时间范围选择 -->
      <div class="time-range-selector">
        <el-radio-group v-model="timeRange" @change="loadResourceData">
          <el-radio-button label="1h">最近1小时</el-radio-button>
          <el-radio-button label="6h">最近6小时</el-radio-button>
          <el-radio-button label="24h">最近24小时</el-radio-button>
          <el-radio-button label="7d">最近7天</el-radio-button>
        </el-radio-group>
        <el-button @click="loadResourceData" :loading="loading" :icon="Refresh">
          刷新
        </el-button>
      </div>

      <!-- 资源概览 -->
      <div class="resource-overview" v-loading="loading">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-card class="overview-card">
              <div class="overview-item">
                <div class="overview-icon cpu">
                  <el-icon><Cpu /></el-icon>
                </div>
                <div class="overview-info">
                  <div class="overview-value">{{ currentMetrics.cpu }}%</div>
                  <div class="overview-label">CPU使用率</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="overview-card">
              <div class="overview-item">
                <div class="overview-icon memory">
                  <el-icon><MemoryCard /></el-icon>
                </div>
                <div class="overview-info">
                  <div class="overview-value">{{ currentMetrics.memory }}%</div>
                  <div class="overview-label">内存使用率</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="overview-card">
              <div class="overview-item">
                <div class="overview-icon network">
                  <el-icon><Connection /></el-icon>
                </div>
                <div class="overview-info">
                  <div class="overview-value">{{ currentMetrics.network }}</div>
                  <div class="overview-label">网络流量</div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card class="overview-card">
              <div class="overview-item">
                <div class="overview-icon disk">
                  <el-icon><HardDrive /></el-icon>
                </div>
                <div class="overview-info">
                  <div class="overview-value">{{ currentMetrics.disk }}%</div>
                  <div class="overview-label">磁盘使用率</div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 图表区域 -->
      <div class="charts-section" v-loading="loading">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <div class="chart-header">
                  <span>CPU使用率趋势</span>
                </div>
              </template>
              <div class="chart-container">
                <div class="mock-chart cpu-chart">
                  <div class="chart-line"></div>
                  <div class="chart-data">
                    <div v-for="i in 10" :key="i" class="data-point" 
                         :style="{ height: Math.random() * 80 + 20 + '%' }"></div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <div class="chart-header">
                  <span>内存使用率趋势</span>
                </div>
              </template>
              <div class="chart-container">
                <div class="mock-chart memory-chart">
                  <div class="chart-line"></div>
                  <div class="chart-data">
                    <div v-for="i in 10" :key="i" class="data-point" 
                         :style="{ height: Math.random() * 60 + 30 + '%' }"></div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <el-row :gutter="16" style="margin-top: 16px;">
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <div class="chart-header">
                  <span>网络流量</span>
                </div>
              </template>
              <div class="chart-container">
                <div class="mock-chart network-chart">
                  <div class="chart-line"></div>
                  <div class="chart-data">
                    <div v-for="i in 10" :key="i" class="data-point" 
                         :style="{ height: Math.random() * 70 + 10 + '%' }"></div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <div class="chart-header">
                  <span>磁盘I/O</span>
                </div>
              </template>
              <div class="chart-container">
                <div class="mock-chart disk-chart">
                  <div class="chart-line"></div>
                  <div class="chart-data">
                    <div v-for="i in 10" :key="i" class="data-point" 
                         :style="{ height: Math.random() * 50 + 20 + '%' }"></div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 实例列表 -->
      <div class="instances-section">
        <el-card class="instances-card">
          <template #header>
            <div class="card-header">
              <el-icon><Grid /></el-icon>
              <span>实例列表</span>
            </div>
          </template>
          
          <el-table :data="instances" stripe>
            <el-table-column prop="name" label="实例名称" width="200" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getInstanceStatusType(row.status)" size="small">
                  {{ getInstanceStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="cpu" label="CPU" width="80">
              <template #default="{ row }">
                <span :class="getMetricClass(row.cpu, 'percentage')">{{ row.cpu }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="memory" label="内存" width="80">
              <template #default="{ row }">
                <span :class="getMetricClass(row.memory, 'percentage')">{{ row.memory }}%</span>
              </template>
            </el-table-column>
            <el-table-column prop="network_in" label="网络入" width="100" />
            <el-table-column prop="network_out" label="网络出" width="100" />
            <el-table-column prop="uptime" label="运行时间" width="120" />
            <el-table-column prop="restart_count" label="重启次数" width="100" />
          </el-table>
        </el-card>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleExport">导出报告</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Refresh, Cpu, MemoryCard, Connection, HardDrive, Grid 
} from '@element-plus/icons-vue'
import { enhancedCmdbApi } from '@/api/modules/enhanced-cmdb'
import type { MicroApp } from '@/api/modules/enhanced-cmdb'

// Props
interface Props {
  visible: boolean
  app?: MicroApp | null
}

const props = withDefaults(defineProps<Props>(), {
  app: null
})

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 响应式数据
const loading = ref(false)
const timeRange = ref('1h')
const currentMetrics = reactive({
  cpu: 0,
  memory: 0,
  network: '0 MB/s',
  disk: 0
})
const instances = ref<any[]>([])

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 监听应用变化
watch(() => props.app, (newApp) => {
  if (newApp) {
    loadResourceData()
  }
})

// 生命周期
onMounted(() => {
  if (props.app) {
    loadResourceData()
  }
})

// 方法
const loadResourceData = async () => {
  if (!props.app) return
  
  try {
    loading.value = true
    
    // 模拟加载资源数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟当前指标
    currentMetrics.cpu = Math.floor(Math.random() * 80) + 10
    currentMetrics.memory = Math.floor(Math.random() * 70) + 20
    currentMetrics.network = `${(Math.random() * 10).toFixed(1)} MB/s`
    currentMetrics.disk = Math.floor(Math.random() * 60) + 20
    
    // 模拟实例数据
    instances.value = Array.from({ length: 3 }, (_, i) => ({
      name: `${props.app!.app_code}-instance-${i + 1}`,
      status: ['running', 'stopped', 'error'][Math.floor(Math.random() * 3)],
      cpu: Math.floor(Math.random() * 80) + 10,
      memory: Math.floor(Math.random() * 70) + 20,
      network_in: `${(Math.random() * 5).toFixed(1)} MB/s`,
      network_out: `${(Math.random() * 3).toFixed(1)} MB/s`,
      uptime: `${Math.floor(Math.random() * 30) + 1}d ${Math.floor(Math.random() * 24)}h`,
      restart_count: Math.floor(Math.random() * 5)
    }))
    
    // 调用后端API
    try {
      await enhancedCmdbApi.getMicroAppResources(props.app!.id!)
    } catch (error) {
      console.error('调用资源监控API失败:', error)
    }
    
  } catch (error: any) {
    ElMessage.error(error.message || '加载资源数据失败')
  } finally {
    loading.value = false
  }
}

const getInstanceStatusType = (status: string) => {
  const types: Record<string, string> = {
    running: 'success',
    stopped: 'warning',
    error: 'danger'
  }
  return types[status] || 'info'
}

const getInstanceStatusText = (status: string) => {
  const texts: Record<string, string> = {
    running: '运行中',
    stopped: '已停止',
    error: '异常'
  }
  return texts[status] || '未知'
}

const getMetricClass = (value: number, type: string = 'time') => {
  if (type === 'percentage') {
    if (value > 80) return 'metric-danger'
    if (value > 60) return 'metric-warning'
    return 'metric-success'
  }
  return ''
}

const handleExport = () => {
  // 模拟导出功能
  ElMessage.success('资源监控报告导出成功')
}

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.resource-monitor-content {
  max-height: 80vh;
  overflow-y: auto;
}

.monitor-alert {
  margin-bottom: 20px;
}

.time-range-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.resource-overview {
  margin-bottom: 20px;
}

.overview-card {
  height: 100px;
}

.overview-item {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 16px;
}

.overview-icon {
  font-size: 32px;
  margin-right: 16px;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overview-icon.cpu {
  background-color: #e6f7ff;
  color: #1890ff;
}

.overview-icon.memory {
  background-color: #f6ffed;
  color: #52c41a;
}

.overview-icon.network {
  background-color: #fff7e6;
  color: #fa8c16;
}

.overview-icon.disk {
  background-color: #fff1f0;
  color: #f5222d;
}

.overview-info {
  flex: 1;
}

.overview-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 12px;
  color: #909399;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 300px;
}

.chart-header {
  font-weight: 500;
}

.chart-container {
  height: 220px;
  position: relative;
}

.mock-chart {
  width: 100%;
  height: 100%;
  position: relative;
  background: linear-gradient(to bottom, #f5f7fa 0%, #ffffff 100%);
  border-radius: 6px;
  overflow: hidden;
}

.chart-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(to right, #e4e7ed 1px, transparent 1px),
    linear-gradient(to bottom, #e4e7ed 1px, transparent 1px);
  background-size: 20px 20px;
}

.chart-data {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  height: 160px;
  display: flex;
  align-items: end;
  gap: 8px;
}

.data-point {
  flex: 1;
  background: linear-gradient(to top, #409eff, #79bbff);
  border-radius: 2px 2px 0 0;
  min-height: 10px;
  animation: chartAnimation 2s ease-in-out;
}

.cpu-chart .data-point {
  background: linear-gradient(to top, #1890ff, #69c0ff);
}

.memory-chart .data-point {
  background: linear-gradient(to top, #52c41a, #95de64);
}

.network-chart .data-point {
  background: linear-gradient(to top, #fa8c16, #ffc069);
}

.disk-chart .data-point {
  background: linear-gradient(to top, #f5222d, #ff7875);
}

@keyframes chartAnimation {
  from {
    height: 0;
  }
  to {
    height: var(--final-height);
  }
}

.instances-section {
  margin-bottom: 20px;
}

.instances-card {
  border: 1px solid #e4e7ed;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.metric-success {
  color: #67c23a;
  font-weight: 500;
}

.metric-warning {
  color: #e6a23c;
  font-weight: 500;
}

.metric-danger {
  color: #f56c6c;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-card__body) {
  padding: 16px;
}
</style>
