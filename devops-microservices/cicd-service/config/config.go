package config

import (
	"github.com/spf13/viper"
)

// Config holds all configuration for the CICD service
type Config struct {
	// Server configuration
	Port     int    `mapstructure:"PORT"`
	LogLevel string `mapstructure:"LOG_LEVEL"`

	// Database configuration
	DBHost     string `mapstructure:"DB_HOST"`
	DBPort     int    `mapstructure:"DB_PORT"`
	DBUser     string `mapstructure:"DB_USER"`
	DBPassword string `mapstructure:"DB_PASSWORD"`
	DBName     string `mapstructure:"DB_NAME"`
	DBSSLMode  string `mapstructure:"DB_SSL_MODE"`

	// Redis configuration
	RedisHost     string `mapstructure:"REDIS_HOST"`
	RedisPort     int    `mapstructure:"REDIS_PORT"`
	RedisPassword string `mapstructure:"REDIS_PASSWORD"`
	RedisDB       int    `mapstructure:"REDIS_DB"`

	// Kubernetes configuration
	KubeConfig string `mapstructure:"KUBE_CONFIG"`

	// Docker configuration
	DockerHost string `mapstructure:"DOCKER_HOST"`

	// Harbor configuration
	HarborURL      string `mapstructure:"HARBOR_URL"`
	HarborUsername string `mapstructure:"HARBOR_USERNAME"`
	HarborPassword string `mapstructure:"HARBOR_PASSWORD"`

	// Git configuration
	GitlabURL   string `mapstructure:"GITLAB_URL"`
	GitlabToken string `mapstructure:"GITLAB_TOKEN"`

	// Notification configuration
	NotificationURL string `mapstructure:"NOTIFICATION_URL"`

	// Tekton configuration
	TektonNamespace string `mapstructure:"TEKTON_NAMESPACE"`
}

// LoadConfig loads configuration from environment variables
func LoadConfig() (*Config, error) {
	viper.AutomaticEnv()

	// Set default values
	viper.SetDefault("PORT", 8080)
	viper.SetDefault("LOG_LEVEL", "info")
	viper.SetDefault("DB_HOST", "localhost")
	viper.SetDefault("DB_PORT", 5432)
	viper.SetDefault("DB_USER", "postgres")
	viper.SetDefault("DB_PASSWORD", "postgres")
	viper.SetDefault("DB_NAME", "devops")
	viper.SetDefault("DB_SSL_MODE", "disable")
	viper.SetDefault("REDIS_HOST", "localhost")
	viper.SetDefault("REDIS_PORT", 6379)
	viper.SetDefault("REDIS_PASSWORD", "")
	viper.SetDefault("REDIS_DB", 0)
	viper.SetDefault("KUBE_CONFIG", "")
	viper.SetDefault("DOCKER_HOST", "unix:///var/run/docker.sock")
	viper.SetDefault("HARBOR_URL", "")
	viper.SetDefault("HARBOR_USERNAME", "")
	viper.SetDefault("HARBOR_PASSWORD", "")
	viper.SetDefault("GITLAB_URL", "")
	viper.SetDefault("GITLAB_TOKEN", "")
	viper.SetDefault("NOTIFICATION_URL", "http://notification-service:8082")
	viper.SetDefault("TEKTON_NAMESPACE", "tekton-pipelines")

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, err
	}

	return &config, nil
}
