package controllers

import (
	"net/http"
	"strconv"

	"github.com/devops-microservices/kubernetes-service/config"
	"github.com/devops-microservices/kubernetes-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedKubernetesController 增强的Kubernetes控制器
type EnhancedKubernetesController struct {
	db           *gorm.DB
	cfg          *config.Config
	log          *logrus.Logger
	enhancedSvc  *services.EnhancedK8sService
	auditSvc     services.AuditService
	wsSvc        services.WebSocketService
}

// NewEnhancedKubernetesController 创建增强的Kubernetes控制器
func NewEnhancedKubernetesController(db *gorm.DB, cfg *config.Config, log *logrus.Logger, auditSvc services.AuditService, wsSvc services.WebSocketService) *EnhancedKubernetesController {
	enhancedSvc := services.NewEnhancedK8sService(db, cfg, log, auditSvc, wsSvc)
	
	return &EnhancedKubernetesController{
		db:          db,
		cfg:         cfg,
		log:         log,
		enhancedSvc: enhancedSvc,
		auditSvc:    auditSvc,
		wsSvc:       wsSvc,
	}
}

// GetClusterMetrics 获取集群指标
// @Tags Kubernetes Enhanced
// @Summary 获取集群详细指标
// @Description 获取集群的详细性能指标、资源使用情况和健康状态
// @Accept json
// @Produce json
// @Param id path string true "集群ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/kubernetes/{id}/metrics [get]
func (c *EnhancedKubernetesController) GetClusterMetrics(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的集群ID",
		})
		return
	}

	metrics, err := c.enhancedSvc.GetClusterMetrics(ctx.Request.Context(), uint(id))
	if err != nil {
		c.log.WithError(err).Error("获取集群指标失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取集群指标失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": metrics,
	})
}

// GetClusterHealth 获取集群健康状态
// @Tags Kubernetes Enhanced
// @Summary 获取集群健康状态
// @Description 获取集群的整体健康状态和组件状态
// @Accept json
// @Produce json
// @Param id path string true "集群ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/kubernetes/{id}/health [get]
func (c *EnhancedKubernetesController) GetClusterHealth(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的集群ID",
		})
		return
	}

	metrics, err := c.enhancedSvc.GetClusterMetrics(ctx.Request.Context(), uint(id))
	if err != nil {
		c.log.WithError(err).Error("获取集群健康状态失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取集群健康状态失败: " + err.Error(),
		})
		return
	}

	healthData := gin.H{
		"status":     metrics.HealthStatus,
		"alerts":     metrics.Alerts,
		"node_count": metrics.NodeCount,
		"pod_count":  metrics.PodCount,
		"timestamp":  "now",
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": healthData,
	})
}

// GetResourceQuotas 获取资源配额
// @Tags Kubernetes Enhanced
// @Summary 获取资源配额信息
// @Description 获取集群中所有命名空间的资源配额使用情况
// @Accept json
// @Produce json
// @Param id path string true "集群ID"
// @Param namespace query string false "命名空间筛选"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/kubernetes/{id}/quotas [get]
func (c *EnhancedKubernetesController) GetResourceQuotas(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的集群ID",
		})
		return
	}

	namespace := ctx.Query("namespace")

	metrics, err := c.enhancedSvc.GetClusterMetrics(ctx.Request.Context(), uint(id))
	if err != nil {
		c.log.WithError(err).Error("获取资源配额失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取资源配额失败: " + err.Error(),
		})
		return
	}

	quotas := metrics.ResourceQuotas
	if namespace != "" {
		filteredQuotas := []services.ResourceQuotaInfo{}
		for _, quota := range quotas {
			if quota.Namespace == namespace {
				filteredQuotas = append(filteredQuotas, quota)
			}
		}
		quotas = filteredQuotas
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"quotas": quotas,
			"total":  len(quotas),
		},
	})
}

// GetTopResources 获取资源使用排行
// @Tags Kubernetes Enhanced
// @Summary 获取资源使用排行
// @Description 获取CPU和内存使用最高的Pod和Node排行榜
// @Accept json
// @Produce json
// @Param id path string true "集群ID"
// @Param type query string false "类型: pods|nodes" default(pods)
// @Param limit query int false "返回数量限制" default(10)
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/kubernetes/{id}/top [get]
func (c *EnhancedKubernetesController) GetTopResources(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的集群ID",
		})
		return
	}

	resourceType := ctx.DefaultQuery("type", "pods")
	limitStr := ctx.DefaultQuery("limit", "10")
	limit, _ := strconv.Atoi(limitStr)

	metrics, err := c.enhancedSvc.GetClusterMetrics(ctx.Request.Context(), uint(id))
	if err != nil {
		c.log.WithError(err).Error("获取资源使用排行失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取资源使用排行失败: " + err.Error(),
		})
		return
	}

	var data interface{}
	switch resourceType {
	case "nodes":
		if len(metrics.TopNodes) > limit {
			data = metrics.TopNodes[:limit]
		} else {
			data = metrics.TopNodes
		}
	case "pods":
		if len(metrics.TopPods) > limit {
			data = metrics.TopPods[:limit]
		} else {
			data = metrics.TopPods
		}
	default:
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的资源类型，支持: pods, nodes",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"type":      resourceType,
			"resources": data,
			"limit":     limit,
		},
	})
}

// GetClusterAlerts 获取集群告警
// @Tags Kubernetes Enhanced
// @Summary 获取集群告警信息
// @Description 获取集群的告警信息，包括节点、Pod等组件的异常状态
// @Accept json
// @Produce json
// @Param id path string true "集群ID"
// @Param level query string false "告警级别筛选: Critical|Warning|Info"
// @Param resolved query bool false "是否包含已解决的告警" default(false)
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/kubernetes/{id}/alerts [get]
func (c *EnhancedKubernetesController) GetClusterAlerts(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的集群ID",
		})
		return
	}

	level := ctx.Query("level")
	resolvedStr := ctx.DefaultQuery("resolved", "false")
	includeResolved := resolvedStr == "true"

	metrics, err := c.enhancedSvc.GetClusterMetrics(ctx.Request.Context(), uint(id))
	if err != nil {
		c.log.WithError(err).Error("获取集群告警失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取集群告警失败: " + err.Error(),
		})
		return
	}

	alerts := []services.ClusterAlert{}
	for _, alert := range metrics.Alerts {
		// 筛选已解决的告警
		if !includeResolved && alert.Resolved {
			continue
		}

		// 筛选告警级别
		if level != "" && alert.Level != level {
			continue
		}

		alerts = append(alerts, alert)
	}

	// 统计告警数量
	alertStats := gin.H{
		"critical": 0,
		"warning":  0,
		"info":     0,
		"total":    len(alerts),
	}

	for _, alert := range alerts {
		switch alert.Level {
		case "Critical":
			alertStats["critical"] = alertStats["critical"].(int) + 1
		case "Warning":
			alertStats["warning"] = alertStats["warning"].(int) + 1
		case "Info":
			alertStats["info"] = alertStats["info"].(int) + 1
		}
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"alerts": alerts,
			"stats":  alertStats,
		},
	})
}

// GetClusterOverview 获取集群概览
// @Tags Kubernetes Enhanced
// @Summary 获取集群概览信息
// @Description 获取集群的概览信息，包括基础统计、资源使用、健康状态等
// @Accept json
// @Produce json
// @Param id path string true "集群ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/kubernetes/{id}/overview [get]
func (c *EnhancedKubernetesController) GetClusterOverview(ctx *gin.Context) {
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的集群ID",
		})
		return
	}

	metrics, err := c.enhancedSvc.GetClusterMetrics(ctx.Request.Context(), uint(id))
	if err != nil {
		c.log.WithError(err).Error("获取集群概览失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取集群概览失败: " + err.Error(),
		})
		return
	}

	// 统计告警数量
	alertCounts := gin.H{
		"critical": 0,
		"warning":  0,
		"info":     0,
	}

	for _, alert := range metrics.Alerts {
		if !alert.Resolved {
			switch alert.Level {
			case "Critical":
				alertCounts["critical"] = alertCounts["critical"].(int) + 1
			case "Warning":
				alertCounts["warning"] = alertCounts["warning"].(int) + 1
			case "Info":
				alertCounts["info"] = alertCounts["info"].(int) + 1
			}
		}
	}

	overview := gin.H{
		"basic_stats": gin.H{
			"node_count":      metrics.NodeCount,
			"pod_count":       metrics.PodCount,
			"namespace_count": metrics.NamespaceCount,
			"service_count":   metrics.ServiceCount,
		},
		"resource_usage": gin.H{
			"cpu_usage":       metrics.CPUUsage,
			"memory_usage":    metrics.MemoryUsage,
			"storage_usage":   metrics.StorageUsage,
			"network_traffic": metrics.NetworkTraffic,
		},
		"health": gin.H{
			"status": metrics.HealthStatus,
			"alerts": alertCounts,
		},
		"top_resources": gin.H{
			"top_pods":  metrics.TopPods[:min(len(metrics.TopPods), 5)],
			"top_nodes": metrics.TopNodes[:min(len(metrics.TopNodes), 5)],
		},
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": overview,
	})
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
