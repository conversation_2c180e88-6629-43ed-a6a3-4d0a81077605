package routes

import (
	"sort"
	"strings"

	"github.com/devops-microservices/kubernetes-service/config"
	"github.com/devops-microservices/kubernetes-service/controllers"
	"github.com/devops-microservices/kubernetes-service/middlewares"
	"github.com/devops-microservices/kubernetes-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/gorm"
)

// SetupRoutes 设置所有路由
func SetupRoutes(router *gin.Engine, db *gorm.DB, cfg *config.Config, log *logrus.Logger) {
	// 初始化服务
	auditService := services.NewAuditService(db, log)
	wsService := services.NewWebSocketService(log)

	// 初始化控制器
	kubernetesController := controllers.NewKubernetesController(db, cfg, auditService, wsService)
	enhancedController := controllers.NewEnhancedKubernetesController(db, cfg, log, auditService, wsService)

	// 创建认证中间件
	authMiddleware := middlewares.JWTAuthMiddleware(cfg, log)

	// ============== 公共路由（无需认证） ==============
	// 健康检查端点
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"service": "kubernetes-service",
			"version": "v1.0.0",
		})
	})

	// Swagger文档路由
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// ============== API v1 路由（需要认证） ==============
	v1 := router.Group("/api/v1/kubernetes")
	v1.Use(authMiddleware)
	// K8s集群相关路由
	{
		v1.GET("", kubernetesController.GetKubernetesClusters)
		v1.GET("/:id", kubernetesController.GetKubernetesCluster)
		v1.POST("", kubernetesController.CreateKubernetesCluster)
		v1.PUT("/:id", kubernetesController.UpdateKubernetesCluster)
		v1.DELETE("/:id", kubernetesController.DeleteKubernetesCluster)
		v1.GET("/:id/config", kubernetesController.GetKubernetesConfig)
		v1.GET("/:id/info", kubernetesController.GetKubernetesInfo)
		v1.GET("/:id/stats", kubernetesController.GetKubernetesStat)
		v1.GET("/:id/pods", kubernetesController.GetKubernetesPods)
		v1.GET("/:id/events", kubernetesController.GetKubernetesEvents)

		// 命名空间操作路由
		v1.POST("/:id/namespaces", kubernetesController.CreateKubernetesNamespace)

		// 工作负载操作路由
		v1.GET("/:id/workloads/:workload_type", kubernetesController.GetKubernetesWorkloads)
		v1.GET("/:id/namespaces/:namespace/workloads/:name", kubernetesController.GetKubernetesWorkload)
		v1.POST("/:id/namespaces/:namespace/workloads", kubernetesController.CreateKubernetesWorkload)
		v1.PUT("/:id/namespaces/:namespace/workloads/:name", kubernetesController.UpdateKubernetesWorkload)
		v1.DELETE("/:id/namespaces/:namespace/workloads/:name", kubernetesController.DeleteKubernetesWorkload)
		v1.POST("/:id/namespaces/:namespace/workloads/:name/scale", kubernetesController.ScaleKubernetesWorkload)
		v1.POST("/:id/namespaces/:namespace/workloads/:name/restart", kubernetesController.RestartKubernetesWorkload) // 重启

		// 导出YAML
		v1.GET("/:id/workloads/yaml", kubernetesController.ExportWorkloadsYaml)

		// Pod 详细操作路由
		v1.GET("/:id/namespaces/:namespace/pods/:name", kubernetesController.GetKubernetesPod)
		v1.GET("/:id/namespaces/:namespace/pods/:name/logs", kubernetesController.GetKubernetesPodLogs)
		v1.DELETE("/:id/namespaces/:namespace/pods/:name", kubernetesController.DeleteKubernetesPod)

		// 批量操作路由
		v1.DELETE("/:id/namespaces/:namespace/pods/batch", kubernetesController.BatchDeleteKubernetesPods)
		v1.POST("/:id/resources/:type/batch", kubernetesController.BatchCreateKubernetesResources)
		v1.PUT("/:id/resources/:type/batch", kubernetesController.BatchUpdateKubernetesResources)
		v1.DELETE("/:id/resources/:type/namespaces/:namespace/batch", kubernetesController.BatchDeleteKubernetesResources)

		// 服务操作路由
		v1.POST("/:id/namespaces/:namespace/services", kubernetesController.CreateKubernetesService)

		// ConfigMap操作路由
		v1.POST("/:id/namespaces/:namespace/configmaps", kubernetesController.CreateKubernetesConfigMap)

		// ============== 增强功能路由 ==============
		// 集群指标和监控
		v1.GET("/:id/metrics", enhancedController.GetClusterMetrics)
		v1.GET("/:id/health", enhancedController.GetClusterHealth)
		v1.GET("/:id/overview", enhancedController.GetClusterOverview)

		// 资源配额和使用情况
		v1.GET("/:id/quotas", enhancedController.GetResourceQuotas)
		v1.GET("/:id/top", enhancedController.GetTopResources)

		// 告警和事件
		v1.GET("/:id/alerts", enhancedController.GetClusterAlerts)
	}

	// WebSocket路由 - Pod日志和终端
	wsGroup := router.Group("/ws")
	wsGroup.Use(authMiddleware)
	{
		// Pod日志WebSocket
		// wsGroup.GET("/kubernetes/:id/namespaces/:namespace/pods/:name/logs", kubernetesController.HandlePodLogsWebSocket)
		// Pod终端WebSocket
		wsGroup.GET("/kubernetes/:id/namespaces/:namespace/pods/:name/terminal", kubernetesController.HandlePodTerminalWebSocket)
	}

	// 通用WebSocket路由
	router.GET("/ws", func(c *gin.Context) {
		// 这里需要在main.go中注入WebSocket服务
		wsService.HandleWebSocket(c)
	})

	// 审计日志路由
	auditGroup := v1.Group("/audit")
	{
		auditGroup.GET("/logs", func(c *gin.Context) {
			// 这里需要在main.go中注入审计服务
			auditService.GetAuditLogs(1, 10, map[string]interface{}{})
		})
	}

	// 打印注册的路由
	printRegisteredRoutes(router, log)
}

// printRegisteredRoutes 打印注册的路由
func printRegisteredRoutes(r *gin.Engine, logger *logrus.Logger) {
	routes := r.Routes()
	if len(routes) == 0 {
		logger.Info("没有注册任何路由")
		return
	}

	// 按路径排序
	sort.Slice(routes, func(i, j int) bool {
		return routes[i].Path < routes[j].Path
	})

	logger.Info("================ 已注册的路由 ================")
	logger.Info("方法    | 路径                                    | 处理器")
	logger.Info("--------|----------------------------------------|------------------")

	for _, route := range routes {
		method := padString(route.Method, 7)
		path := padString(route.Path, 39)
		handler := getHandlerName(route.Handler)
		logger.Infof("%s | %s | %s", method, path, handler)
	}

	logger.Info("=============================================")
	logger.Infof("总计: %d 个路由", len(routes))
	logger.Info("=============================================")

	// 按版本分组统计
	v1Count := 0
	publicCount := 0

	for _, route := range routes {
		if strings.HasPrefix(route.Path, "/api/v1/") {
			v1Count++
		} else {
			publicCount++
		}
	}

	logger.Infof("API v1: %d 个路由", v1Count)
	logger.Infof("公共路由: %d 个路由", publicCount)
}

// padString 字符串填充到指定长度
func padString(s string, length int) string {
	if len(s) >= length {
		return s
	}
	return s + strings.Repeat(" ", length-len(s))
}

// getHandlerName 获取处理器名称
func getHandlerName(handler string) string {
	parts := strings.Split(handler, "/")
	if len(parts) > 0 {
		lastPart := parts[len(parts)-1]
		// 移除包名前缀
		if idx := strings.LastIndex(lastPart, "."); idx != -1 {
			return lastPart[idx+1:]
		}
		return lastPart
	}
	return handler
}
