package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"github.com/devops-microservices/kubernetes-service/models"
	appsv1 "k8s.io/api/apps/v1"
	autoscalingv1 "k8s.io/api/autoscaling/v1"
	batchv1 "k8s.io/api/batch/v1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	metricsv "k8s.io/metrics/pkg/client/clientset/versioned"
)

// K8sClient 是Kubernetes客户端的包装器
type K8sClient struct {
	clientset  *kubernetes.Clientset
	restConfig *rest.Config
	cluster    *models.KubernetesCluster
}

// GetClientset 获取Kubernetes客户端集
func (k *K8sClient) GetClientset() *kubernetes.Clientset {
	return k.clientset
}

// GetRestConfig 获取REST配置
func (k *K8sClient) GetRestConfig() *rest.Config {
	return k.restConfig
}

// GetCluster 获取集群信息
func (k *K8sClient) GetCluster() *models.KubernetesCluster {
	return k.cluster
}

// CreateMetricsClient 创建Metrics客户端
func CreateMetricsClient(kubeconfig string) (*metricsv.Clientset, error) {
	var config *rest.Config
	var err error

	if kubeconfig == "" {
		// 使用集群内配置
		config, err = rest.InClusterConfig()
	} else {
		// 使用kubeconfig文件
		config, err = clientcmd.RESTConfigFromKubeConfig([]byte(kubeconfig))
	}

	if err != nil {
		return nil, err
	}

	metricsClient, err := metricsv.NewForConfig(config)
	if err != nil {
		return nil, err
	}

	return metricsClient, nil
}

// EncryptKubernetesConfig 加密Kubernetes配置
func EncryptKubernetesConfig(secretKey string, salt string, info string, config interface{}) (map[string]interface{}, error) {
	fmt.Printf("🔧 开始加密Kubernetes配置\n")

	var configStr string
	switch v := config.(type) {
	case string:
		configStr = v
	case []byte:
		configStr = string(v)
	case map[string]interface{}:
		// 如果是 map，转成 JSON 字符串
		b, err := json.Marshal(v)
		if err != nil {
			return nil, fmt.Errorf("配置格式错误: %v", err)
		}
		configStr = string(b)
	default:
		return nil, fmt.Errorf("不支持的配置类型: %T", config)
	}

	ef, err := NewEncryptedField([]string{secretKey}, salt, info)
	if err != nil {
		return nil, fmt.Errorf("创建EncryptedField失败: %v", err)
	}

	encryptedData, err := ef.Encrypt([]byte(configStr))
	if err != nil {
		return nil, fmt.Errorf("加密失败: %v", err)
	}

	return map[string]interface{}{
		"type":       "config",
		"kubeconfig": string(encryptedData),
	}, nil
}

// decryptKubernetesConfig 解密Kubernetes配置
func DecryptKubernetesConfig(secretKey string, salt string, info string, encryptedConfig interface{}) (map[string]interface{}, error) {
	fmt.Printf("🔧 开始解密Kubernetes配置\n")

	// 将配置转换为字符串
	var configStr string
	switch v := encryptedConfig.(type) {
	case string:
		configStr = v
	case map[string]interface{}:
		// 如果已经是map，检查是否有明确的配置类型
		// if configType, ok := v["type"].(string); ok {
		// 	switch configType {
		// 	case "config", "basic":
		// 		return v, nil // 已经是解密后的格式
		// 	}
		// }
		configStr = v["kubeconfig"].(string)
		// 尝试将map转换为JSON字符串
		// configBytes, err := json.Marshal(v)
		// if err != nil {
		// 	return nil, fmt.Errorf("配置格式错误: %v", err)
		// }
		// configStr = string(configBytes)
	default:
		return nil, fmt.Errorf("不支持的配置类型: %T", encryptedConfig)
	}

	// 检查配置是否为空
	if configStr == "" {
		return nil, fmt.Errorf("配置为空")
	}

	// 首先尝试检查是否为直接的Fernet加密字符串
	// Fernet token以 "gAAAAA" 开头（Base64编码后的0x80000000开头）
	if len(configStr) > 50 && (configStr[:6] == "gAAAAA" || configStr[:4] == "gAAA") {
		fmt.Printf("🔐 检测到直接的Fernet加密字符串，尝试解密...\n")

		// 使用EncryptedField解密
		ef, err := NewEncryptedField([]string{secretKey}, salt, info)
		if err != nil {
			fmt.Printf("❌ 创建EncryptedField失败: %v\n", err)
			return nil, fmt.Errorf("创建EncryptedField失败: %v", err)
		}

		decryptedData, err := ef.Decrypt([]byte(configStr))
		if err != nil {
			fmt.Printf("❌ Fernet解密失败: %v\n", err)
			return nil, fmt.Errorf("Fernet解密失败: %v", err)
		}

		fmt.Printf("✅ Fernet解密成功\n")

		// 尝试解析为JSON
		var decryptedConfig interface{}
		if err := json.Unmarshal(decryptedData, &decryptedConfig); err != nil {
			// 如果不是JSON，作为字符串处理
			fmt.Printf("📝 解密数据不是JSON格式，作为kubeconfig字符串处理\n")
			return map[string]interface{}{
				"type":       "config",
				"kubeconfig": string(decryptedData),
			}, nil
		}

		// 如果是JSON，检查是否是K8s配置格式
		if configMap, ok := decryptedConfig.(map[string]interface{}); ok {
			if configType, exists := configMap["type"]; exists {
				fmt.Printf("📋 解密后配置类型: %v\n", configType)

				// 检查配置类型并转换为标准格式
				switch configType {
				case "config":
					// 如果有config字段，将其重命名为kubeconfig
					if configContent, hasConfig := configMap["config"]; hasConfig {
						return map[string]interface{}{
							"type":       "config",
							"kubeconfig": configContent,
						}, nil
					}
					// 如果已经有kubeconfig字段，直接返回
					if _, hasKubeconfig := configMap["kubeconfig"]; hasKubeconfig {
						return configMap, nil
					}
					// 否则作为整个内容处理
					configBytes, _ := json.Marshal(configMap)
					return map[string]interface{}{
						"type":       "config",
						"kubeconfig": string(configBytes),
					}, nil
				case "basic":
					// 基础认证配置，直接返回
					return configMap, nil
				default:
					// 其他类型，作为kubeconfig处理
					configBytes, _ := json.Marshal(configMap)
					return map[string]interface{}{
						"type":       "config",
						"kubeconfig": string(configBytes),
					}, nil
				}
			}
		}

		// 默认作为kubeconfig处理
		return map[string]interface{}{
			"type":       "config",
			"kubeconfig": string(decryptedData),
		}, nil
	}

	// 尝试解析为JSON格式配置
	fmt.Printf("📋 尝试解析为JSON格式配置...\n")
	var configType struct {
		Type   string      `json:"type"`
		Config interface{} `json:"config"`
	}

	if err := json.Unmarshal([]byte(configStr), &configType); err != nil {
		fmt.Printf("❌ JSON解析失败: %v\n", err)

		// 如果JSON解析失败，尝试直接作为配置使用
		fmt.Printf("⚠️ JSON解析失败，尝试将配置作为明文kubeconfig使用\n")
		return map[string]interface{}{
			"type":       "config",
			"kubeconfig": configStr,
		}, nil
	}

	fmt.Printf("✅ JSON解析成功，Kubernetes配置类型: %s\n", configType.Type)

	// 根据配置类型处理
	switch configType.Type {
	case "basic":
		// basic auth或token auth，配置已经明文
		fmt.Printf("🔧 处理基础认证配置...\n")
		if config, ok := configType.Config.(map[string]interface{}); ok {
			fmt.Printf("✅ 基础认证配置解析成功\n")
			return map[string]interface{}{
				"type":     "basic",
				"host":     config["host"],
				"username": config["username"],
				"password": config["password"],
				"token":    config["token"],
				"insecure": config["insecure"],
			}, nil
		}
		return nil, fmt.Errorf("无效的basic配置格式")

	case "config":
		// kubeconfig格式，可能需要解密
		fmt.Printf("📝 处理kubeconfig格式配置...\n")
		if configData, ok := configType.Config.(string); ok {
			// 检查是否为Fernet加密的字符串
			if len(configData) > 50 && (configData[:6] == "gAAAAA" || configData[:4] == "gAAA") {
				fmt.Printf("🔐 检测到Fernet加密的kubeconfig，尝试解密...\n")

				// 获取加密配置
				secretKey := "7f=5@e+a=b(ghm-l*mtc_ile60xuvxqi(l5y$3&gfpk1!)3_4v" // 默认使用Django SECRET_KEY
				salt := "django-fernet-fields-hkdf-salt"                          // 默认盐值
				info := "django-fernet-fields"                                    // 默认info

				// 使用Fernet解密
				ef, err := NewEncryptedField([]string{secretKey}, salt, info)
				if err != nil {
					fmt.Printf("❌ 创建EncryptedField失败: %v\n", err)
					return nil, fmt.Errorf("创建EncryptedField失败: %v", err)
				}

				decryptedData, err := ef.Decrypt([]byte(configData))
				if err != nil {
					fmt.Printf("⚠️ Fernet解密失败，尝试直接使用配置: %v\n", err)
					// 如果解密失败，尝试直接使用
					return map[string]interface{}{
						"type":       "config",
						"kubeconfig": configData,
					}, nil
				} else {
					fmt.Printf("✅ Fernet解密成功\n")
					return map[string]interface{}{
						"type":       "config",
						"kubeconfig": string(decryptedData),
					}, nil
				}
			} else {
				fmt.Printf("📝 检测到明文kubeconfig，直接使用...\n")
				// 直接使用明文配置
				return map[string]interface{}{
					"type":       "config",
					"kubeconfig": configData,
				}, nil
			}
		}
		return nil, fmt.Errorf("无效的config配置格式")

	default:
		return nil, fmt.Errorf("不支持的Kubernetes配置类型: %s", configType.Type)
	}
}

// NewK8sClient 创建一个新的Kubernetes客户端
func NewK8sClient(secretKey string, salt string, info string, cluster *models.KubernetesCluster) (*K8sClient, error) {
	var config *rest.Config
	var err error

	// 从集群配置中解析认证信息
	if cluster.ConfigData.Data == nil {
		return nil, fmt.Errorf("集群配置为空")
	}

	// 解密配置
	configData, err := DecryptKubernetesConfig(secretKey, salt, info, cluster.ConfigData.Data)
	if err != nil {
		return nil, fmt.Errorf("无法解析集群配置: %v", err)
	}

	// 检查配置类型
	configType, _ := configData["type"].(string)

	switch configType {
	case "config":
		// 使用kubeconfig文件
		kubeconfig, _ := configData["kubeconfig"].(string)
		if kubeconfig == "" {
			return nil, fmt.Errorf("kubeconfig配置为空")
		}

		// 创建临时文件保存kubeconfig
		tmpDir, err := os.MkdirTemp("", "k8s-config")
		if err != nil {
			return nil, fmt.Errorf("创建临时目录失败: %v", err)
		}
		defer os.RemoveAll(tmpDir)

		kubeconfigPath := filepath.Join(tmpDir, "config")
		if err := os.WriteFile(kubeconfigPath, []byte(kubeconfig), 0600); err != nil {
			return nil, fmt.Errorf("写入kubeconfig文件失败: %v", err)
		}

		config, err = clientcmd.BuildConfigFromFlags("", kubeconfigPath)
		if err != nil {
			return nil, fmt.Errorf("构建kubeconfig失败: %v", err)
		}
	case "basic":
		// 使用基本认证
		host, _ := configData["host"].(string)
		username, _ := configData["username"].(string)
		password, _ := configData["password"].(string)
		token, _ := configData["token"].(string)
		insecure, _ := configData["insecure"].(bool)

		if host == "" {
			return nil, fmt.Errorf("Kubernetes API服务器地址为空")
		}

		config = &rest.Config{
			Host:        host,
			Username:    username,
			Password:    password,
			BearerToken: token,
			TLSClientConfig: rest.TLSClientConfig{
				Insecure: insecure,
			},
		}
	default:
		return nil, fmt.Errorf("不支持的认证类型: %s", configType)
	}

	// 创建Kubernetes客户端
	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		return nil, fmt.Errorf("创建Kubernetes客户端失败: %v", err)
	}

	return &K8sClient{
		clientset:  clientset,
		restConfig: config,
		cluster:    cluster,
	}, nil
}

// GetNodes 获取节点列表
func (k *K8sClient) GetNodes(limit int, continueToken string) (map[string]interface{}, error) {
	ctx := context.Background()
	listOptions := metav1.ListOptions{
		Limit: int64(limit),
	}
	if continueToken != "" {
		listOptions.Continue = continueToken
	}

	nodeList, err := k.clientset.CoreV1().Nodes().List(ctx, listOptions)
	if err != nil {
		return nil, fmt.Errorf("获取节点列表失败: %v", err)
	}

	result := map[string]interface{}{
		"items": nodeList.Items,
		"metadata": map[string]interface{}{
			"continue": nodeList.Continue,
		},
	}
	return result, nil
}

// GetNamespaces 获取命名空间列表
func (k *K8sClient) GetNamespaces(limit int, continueToken string) (map[string]interface{}, error) {
	ctx := context.Background()
	listOptions := metav1.ListOptions{
		Limit: int64(limit),
	}
	if continueToken != "" {
		listOptions.Continue = continueToken
	}

	namespaceList, err := k.clientset.CoreV1().Namespaces().List(ctx, listOptions)
	if err != nil {
		return nil, fmt.Errorf("获取命名空间列表失败: %v", err)
	}

	result := map[string]interface{}{
		"items": namespaceList.Items,
		"metadata": map[string]interface{}{
			"continue": namespaceList.Continue,
		},
	}
	return result, nil
}

// GetServices 获取服务列表
func (k *K8sClient) GetServices(namespace string, limit int, continueToken string) (map[string]interface{}, error) {
	ctx := context.Background()
	listOptions := metav1.ListOptions{
		Limit: int64(limit),
	}
	if continueToken != "" {
		listOptions.Continue = continueToken
	}

	serviceList, err := k.clientset.CoreV1().Services(namespace).List(ctx, listOptions)
	if err != nil {
		return nil, fmt.Errorf("获取服务列表失败: %v", err)
	}

	result := map[string]interface{}{
		"items": serviceList.Items,
		"metadata": map[string]interface{}{
			"continue": serviceList.Continue,
		},
	}
	return result, nil
}

// GetDeploymentsAll 获取所有部署数量统计
func (k *K8sClient) GetDeploymentsAll() (map[string]interface{}, error) {
	ctx := context.Background()
	listOptions := metav1.ListOptions{
		Limit: int64(10000),
	}

	deploymentList, err := k.clientset.AppsV1().Deployments("").List(ctx, listOptions)
	if err != nil {
		return nil, fmt.Errorf("获取部署列表失败: %v", err)
	}

	result := map[string]interface{}{
		"total": len(deploymentList.Items),
	}

	return result, nil
}

// GetWorkload 获取Workload
func (k *K8sClient) GetWorkload(namespace string, name string) (map[string]interface{}, error) {
	ctx := context.Background()
	workload, err := k.clientset.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取Workload失败: %v", err)
	}

	result := map[string]interface{}{
		"message": workload,
	}
	return result, nil
}

// GetDeployments 获取部署列表
func (k *K8sClient) GetDeployments(namespace string, apiVersion string, limit int, continueToken string) (map[string]interface{}, error) {
	ctx := context.Background()
	listOptions := metav1.ListOptions{
		Limit: int64(limit),
	}
	if continueToken != "" {
		listOptions.Continue = continueToken
	}

	// 使用动态客户端可以处理不同版本的API
	deploymentList, err := k.clientset.AppsV1().Deployments(namespace).List(ctx, listOptions)
	if err != nil {
		return nil, fmt.Errorf("获取部署列表失败: %v", err)
	}

	result := map[string]interface{}{
		"items": deploymentList.Items,
		"metadata": map[string]interface{}{
			"continue": deploymentList.Continue,
		},
	}
	return result, nil
}

// GetStatefulSets 获取StatefulSet列表
func (k *K8sClient) GetStatefulSets(namespace string, apiVersion string, limit int, continueToken string) (map[string]interface{}, error) {
	ctx := context.Background()
	listOptions := metav1.ListOptions{
		Limit: int64(limit),
	}
	if continueToken != "" {
		listOptions.Continue = continueToken
	}

	statefulSetList, err := k.clientset.AppsV1().StatefulSets(namespace).List(ctx, listOptions)
	if err != nil {
		return nil, fmt.Errorf("获取StatefulSet列表失败: %v", err)
	}

	result := map[string]interface{}{
		"items": statefulSetList.Items,
		"metadata": map[string]interface{}{
			"continue": statefulSetList.Continue,
		},
	}
	return result, nil
}

// GetDaemonSets 获取DaemonSet列表
func (k *K8sClient) GetDaemonSets(namespace string, apiVersion string, limit int, continueToken string) (map[string]interface{}, error) {
	ctx := context.Background()
	listOptions := metav1.ListOptions{
		Limit: int64(limit),
	}
	if continueToken != "" {
		listOptions.Continue = continueToken
	}

	daemonSetList, err := k.clientset.AppsV1().DaemonSets(namespace).List(ctx, listOptions)
	if err != nil {
		return nil, fmt.Errorf("获取DaemonSet列表失败: %v", err)
	}

	result := map[string]interface{}{
		"items": daemonSetList.Items,
		"metadata": map[string]interface{}{
			"continue": daemonSetList.Continue,
		},
	}
	return result, nil
}

// GetJobs 获取Job列表
func (k *K8sClient) GetJobs(namespace string, apiVersion string, limit int, continueToken string) (map[string]interface{}, error) {
	ctx := context.Background()
	listOptions := metav1.ListOptions{
		Limit: int64(limit),
	}
	if continueToken != "" {
		listOptions.Continue = continueToken
	}

	jobList, err := k.clientset.BatchV1().Jobs(namespace).List(ctx, listOptions)
	if err != nil {
		return nil, fmt.Errorf("获取Job列表失败: %v", err)
	}

	result := map[string]interface{}{
		"items": jobList.Items,
		"metadata": map[string]interface{}{
			"continue": jobList.Continue,
		},
	}
	return result, nil
}

// GetCronJobs 获取CronJob列表
func (k *K8sClient) GetCronJobs(namespace string, apiVersion string, limit int, continueToken string) (map[string]interface{}, error) {
	ctx := context.Background()
	listOptions := metav1.ListOptions{
		Limit: int64(limit),
	}
	if continueToken != "" {
		listOptions.Continue = continueToken
	}

	cronJobList, err := k.clientset.BatchV1().CronJobs(namespace).List(ctx, listOptions)
	if err != nil {
		return nil, fmt.Errorf("获取CronJob列表失败: %v", err)
	}

	result := map[string]interface{}{
		"items": cronJobList.Items,
		"metadata": map[string]interface{}{
			"continue": cronJobList.Continue,
		},
	}
	return result, nil
}

// GetPods 获取Pod列表
func (k *K8sClient) GetPods(namespace string, limit int, continueToken string, labelSelector string, fieldSelector string) (map[string]interface{}, error) {
	ctx := context.Background()
	listOptions := metav1.ListOptions{
		Limit: int64(limit),
	}
	if continueToken != "" {
		listOptions.Continue = continueToken
	}
	if labelSelector != "" {
		listOptions.LabelSelector = labelSelector
	}

	if fieldSelector != "" {
		listOptions.FieldSelector = fieldSelector
	}

	podList, err := k.clientset.CoreV1().Pods(namespace).List(ctx, listOptions)
	if err != nil {
		return nil, fmt.Errorf("获取Pod列表失败: %v", err)
	}

	result := map[string]interface{}{
		"items": podList.Items,
		"metadata": map[string]interface{}{
			"continue": podList.Continue,
		},
	}
	return result, nil
}

// GetConfigMaps 获取ConfigMap列表
func (k *K8sClient) GetConfigMaps(namespace string, limit int, continueToken string) (map[string]interface{}, error) {
	ctx := context.Background()
	listOptions := metav1.ListOptions{
		Limit: int64(limit),
	}
	if continueToken != "" {
		listOptions.Continue = continueToken
	}

	configMapList, err := k.clientset.CoreV1().ConfigMaps(namespace).List(ctx, listOptions)
	if err != nil {
		return nil, fmt.Errorf("获取ConfigMap列表失败: %v", err)
	}

	result := map[string]interface{}{
		"items": configMapList.Items,
		"metadata": map[string]interface{}{
			"continue": configMapList.Continue,
		},
	}
	return result, nil
}

// GetDeploymentInfo 获取部署详情
func (k *K8sClient) GetDeploymentInfo(namespace string, name string, apiVersion string) (map[string]interface{}, error) {
	ctx := context.Background()
	deployment, err := k.clientset.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取部署详情失败: %v", err)
	}

	result := map[string]interface{}{
		"message": deployment,
	}
	return result, nil
}

// GetServiceInfo 获取服务详情
func (k *K8sClient) GetServiceInfo(namespace string, name string) (map[string]interface{}, error) {
	ctx := context.Background()
	service, err := k.clientset.CoreV1().Services(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取服务详情失败: %v", err)
	}

	result := map[string]interface{}{
		"message": service,
	}
	return result, nil
}

// GetPodsForDeployment 获取部署相关的Pod
func (k *K8sClient) GetPodsForDeployment(namespace string, deploymentName string) (map[string]interface{}, error) {
	ctx := context.Background()
	labelSelector := fmt.Sprintf("app=%s", deploymentName)
	pods, err := k.clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		return nil, fmt.Errorf("获取部署相关的Pod失败: %v", err)
	}

	result := map[string]interface{}{
		"message": pods,
	}
	return result, nil
}

// GetEachPodsMetrics 获取每个Pod的资源使用情况
func (k *K8sClient) GetEachPodsMetrics(namespaces []string) (map[string]interface{}, error) {
	metricsClient, err := metricsv.NewForConfig(k.restConfig)
	if err != nil {
		return nil, fmt.Errorf("创建metrics client失败: %v", err)
	}

	podMetrics := make(map[string]interface{})

	for _, ns := range namespaces {
		podMetricsList, err := metricsClient.MetricsV1beta1().PodMetricses(ns).List(context.Background(), metav1.ListOptions{})
		if err != nil {
			return nil, fmt.Errorf("获取Pod metrics失败: %v", err)
		}

		for _, m := range podMetricsList.Items {
			podUsage := map[string]interface{}{}
			podCPU := 0.0
			podMemory := 0.0

			// containers := make([]map[string]interface{}, 0)
			for _, c := range m.Containers {
				cpuQuantity := c.Usage.Cpu()
				memQuantity := c.Usage.Memory()

				cpuCores := float64(cpuQuantity.MilliValue())
				memoryUsage := float64(memQuantity.Value()) // / (1024 * 1024)

				podCPU += cpuCores
				podMemory += memoryUsage

				// containers = append(containers, map[string]interface{}{
				// 	"name":   c.Name,
				// 	"cpu":    cpuCores,
				// 	"memory": memoryUsage,
				// })
			}

			podUsage["cpu"] = podCPU
			podUsage["memory"] = podMemory
			// podUsage["containers"] = containers
			podUsage["namespace"] = ns

			podMetrics[m.Name] = podUsage
		}
	}

	fmt.Println(podMetrics, "podMetrics=========")

	return podMetrics, nil
}

// GetPodsMetrics 获取Pod的资源使用情况（需 metrics-server 支持）
func (k *K8sClient) GetPodsMetrics(namespaces []string) (map[string]interface{}, error) {
	metricsClient, err := metricsv.NewForConfig(k.restConfig)
	if err != nil {
		return nil, fmt.Errorf("创建metrics client失败: %v", err)
	}

	// 统计总体资源使用情况
	totalCPU := 0.0
	totalMemory := 0.0
	podCount := 0

	result := make(map[string]interface{})
	podMetrics := make(map[string]interface{})

	for _, ns := range namespaces {
		podMetricsList, err := metricsClient.MetricsV1beta1().PodMetricses(ns).List(context.Background(), metav1.ListOptions{})
		if err != nil {
			// 如果metrics-server不可用，返回模拟数据
			fmt.Printf("警告: 获取Pod metrics失败: %v，使用模拟数据\n", err)
			return map[string]interface{}{
				"cpu":     60.0,
				"memory":  65.0,
				"storage": 45.0,
				"pods":    podMetrics,
			}, nil
		}

		for _, m := range podMetricsList.Items {
			podUsage := map[string]interface{}{}
			podCPU := 0.0
			podMemory := 0.0

			containers := make([]map[string]interface{}, 0)
			for _, c := range m.Containers {
				cpuQuantity := c.Usage.Cpu()
				memQuantity := c.Usage.Memory()

				// 转换为标准单位
				cpuCores := float64(cpuQuantity.MilliValue())
				memoryMB := float64(memQuantity.Value()) / (1024 * 1024)

				podCPU += cpuCores
				podMemory += memoryMB

				containers = append(containers, map[string]interface{}{
					"name":   c.Name,
					"cpu":    cpuCores,
					"memory": memoryMB,
				})
			}

			podUsage["cpu"] = podCPU
			podUsage["memory"] = podMemory
			podUsage["containers"] = containers
			podUsage["namespace"] = ns

			podMetrics[m.Name] = podUsage

			totalCPU += podCPU
			totalMemory += podMemory
			podCount++
		}
	}

	// 计算使用率百分比（基于假设的集群总资源）
	// 这里可以根据实际节点资源进行计算
	cpuUsagePercent := (totalCPU / 10.0) * 100          // 假设集群有10核
	memoryUsagePercent := (totalMemory / 10240.0) * 100 // 假设集群有10GB内存

	// 存储使用率需要通过其他方式获取，这里使用模拟值
	storageUsagePercent := 45.0

	result["cpu"] = cpuUsagePercent
	result["memory"] = memoryUsagePercent
	result["storage"] = storageUsagePercent
	result["pods"] = podMetrics
	result["pod_count"] = podCount
	result["total_cpu_cores"] = totalCPU
	result["total_memory_mb"] = totalMemory

	return result, nil
}

// GetNodeMetrics 获取节点级别的资源使用情况
func (k *K8sClient) GetNodeMetrics() (map[string]interface{}, error) {
	metricsClient, err := metricsv.NewForConfig(k.restConfig)
	if err != nil {
		return nil, fmt.Errorf("创建metrics client失败: %v", err)
	}

	nodeMetricsList, err := metricsClient.MetricsV1beta1().NodeMetricses().List(context.Background(), metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取节点metrics失败: %v", err)
	}

	totalCPU := 0.0
	totalMemory := 0.0
	nodeCount := len(nodeMetricsList.Items)
	nodes := make([]map[string]interface{}, 0)

	for _, nodeMetric := range nodeMetricsList.Items {
		cpuQuantity := nodeMetric.Usage.Cpu()
		memQuantity := nodeMetric.Usage.Memory()

		cpuCores := float64(cpuQuantity.MilliValue())
		memoryMB := float64(memQuantity.Value()) / (1024 * 1024)

		totalCPU += cpuCores
		totalMemory += memoryMB

		nodes = append(nodes, map[string]interface{}{
			"name":   nodeMetric.Name,
			"cpu":    cpuCores,
			"memory": memoryMB,
		})
	}

	return map[string]interface{}{
		"total_cpu_cores": totalCPU,
		"total_memory_mb": totalMemory,
		"node_count":      nodeCount,
		"nodes":           nodes,
	}, nil
}

// GetClusterResourceUsage 获取集群整体资源使用情况
func (k *K8sClient) GetClusterResourceUsage(namespaces []string) (map[string]interface{}, error) {
	// 获取节点资源信息
	nodeList, err := k.clientset.CoreV1().Nodes().List(context.Background(), metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取节点列表失败: %v", err)
	}

	// 计算节点总资源
	totalAllocatableCPU := 0.0
	totalAllocatableMemory := 0.0

	for _, node := range nodeList.Items {
		if cpuQuantity, ok := node.Status.Allocatable[v1.ResourceCPU]; ok {
			totalAllocatableCPU += float64(cpuQuantity.MilliValue()) / 1000.0
		}
		if memQuantity, ok := node.Status.Allocatable[v1.ResourceMemory]; ok {
			totalAllocatableMemory += float64(memQuantity.Value()) / (1024 * 1024)
		}
	}

	// 获取实际使用情况
	nodeMetrics, err := k.GetNodeMetrics()
	if err != nil {
		// 如果获取失败，使用模拟数据
		return map[string]interface{}{
			"cpu_usage_percent":     60.0,
			"memory_usage_percent":  65.0,
			"storage_usage_percent": 45.0,
			"allocatable_cpu":       totalAllocatableCPU,
			"allocatable_memory":    totalAllocatableMemory,
			"used_cpu":              totalAllocatableCPU * 0.6,
			"used_memory":           totalAllocatableMemory * 0.65,
		}, nil
	}

	usedCPU := nodeMetrics["total_cpu_cores"].(float64)
	usedMemory := nodeMetrics["total_memory_mb"].(float64)

	cpuUsagePercent := (usedCPU / totalAllocatableCPU) * 100
	memoryUsagePercent := (usedMemory / totalAllocatableMemory) * 100

	return map[string]interface{}{
		"cpu_usage_percent":     cpuUsagePercent,
		"memory_usage_percent":  memoryUsagePercent,
		"storage_usage_percent": 45.0, // 存储使用率需要额外实现
		"allocatable_cpu":       totalAllocatableCPU,
		"allocatable_memory":    totalAllocatableMemory,
		"used_cpu":              usedCPU,
		"used_memory":           usedMemory,
		"node_metrics":          nodeMetrics,
	}, nil
}

// GetPodLogs 获取Pod日志
func (k *K8sClient) GetPodLogs(namespace string, podName string, containerName string) (string, error) {
	ctx := context.Background()

	// 构建日志请求选项
	logOptions := &v1.PodLogOptions{
		Container: containerName,
		Follow:    false,
		TailLines: func(i int64) *int64 { return &i }(100), // 获取最后100行日志
	}

	// 如果没有指定容器名称，获取第一个容器的日志
	if containerName == "" {
		pod, err := k.clientset.CoreV1().Pods(namespace).Get(ctx, podName, metav1.GetOptions{})
		if err != nil {
			return "", fmt.Errorf("获取Pod信息失败: %v", err)
		}
		if len(pod.Spec.Containers) > 0 {
			logOptions.Container = pod.Spec.Containers[0].Name
		}
	}

	// 获取日志流
	req := k.clientset.CoreV1().Pods(namespace).GetLogs(podName, logOptions)
	podLogs, err := req.Stream(ctx)
	if err != nil {
		return "", fmt.Errorf("获取Pod日志流失败: %v", err)
	}
	defer podLogs.Close()

	// 读取日志内容
	logs, err := io.ReadAll(podLogs)
	if err != nil {
		return "", fmt.Errorf("读取Pod日志失败: %v", err)
	}

	return string(logs), nil
}

// DeletePod 删除Pod
func (k *K8sClient) DeletePod(namespace string, podName string) error {
	ctx := context.Background()

	deleteOptions := metav1.DeleteOptions{
		GracePeriodSeconds: func(i int64) *int64 { return &i }(30), // 30秒优雅删除
	}

	err := k.clientset.CoreV1().Pods(namespace).Delete(ctx, podName, deleteOptions)
	if err != nil {
		return fmt.Errorf("删除Pod失败: %v", err)
	}

	return nil
}

// CreateNamespace 创建命名空间
func (k *K8sClient) CreateNamespace(data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()

	name, ok := data["name"].(string)
	if !ok || name == "" {
		return nil, fmt.Errorf("命名空间名称不能为空")
	}

	// 构建命名空间对象
	namespace := &v1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: name,
		},
	}

	// 添加标签（如果有）
	if labels, ok := data["labels"].(map[string]interface{}); ok {
		namespace.ObjectMeta.Labels = make(map[string]string)
		for k, v := range labels {
			if str, ok := v.(string); ok {
				namespace.ObjectMeta.Labels[k] = str
			}
		}
	}

	// 创建命名空间
	createdNS, err := k.clientset.CoreV1().Namespaces().Create(ctx, namespace, metav1.CreateOptions{})
	if err != nil {
		return nil, fmt.Errorf("创建命名空间失败: %v", err)
	}

	result := map[string]interface{}{
		"name":   createdNS.Name,
		"status": createdNS.Status.Phase,
		"uid":    string(createdNS.UID),
	}

	return result, nil
}

// CreateService 创建服务
func (k *K8sClient) CreateService(namespace string, data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()

	name, ok := data["name"].(string)
	if !ok || name == "" {
		return nil, fmt.Errorf("服务名称不能为空")
	}

	serviceType, _ := data["type"].(string)
	if serviceType == "" {
		serviceType = "ClusterIP"
	}

	// 构建服务对象
	service := &v1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Spec: v1.ServiceSpec{
			Type: v1.ServiceType(serviceType),
		},
	}

	// 添加端口配置
	if ports, ok := data["ports"].([]interface{}); ok {
		for _, p := range ports {
			if portMap, ok := p.(map[string]interface{}); ok {
				port := v1.ServicePort{}
				if portNum, ok := portMap["port"].(float64); ok {
					port.Port = int32(portNum)
				}
				if targetPort, ok := portMap["targetPort"].(float64); ok {
					port.TargetPort.IntVal = int32(targetPort)
				}
				if protocol, ok := portMap["protocol"].(string); ok {
					port.Protocol = v1.Protocol(protocol)
				} else {
					port.Protocol = v1.ProtocolTCP
				}
				service.Spec.Ports = append(service.Spec.Ports, port)
			}
		}
	}

	// 添加选择器
	if selector, ok := data["selector"].(map[string]interface{}); ok {
		service.Spec.Selector = make(map[string]string)
		for k, v := range selector {
			if str, ok := v.(string); ok {
				service.Spec.Selector[k] = str
			}
		}
	}

	// 创建服务
	createdSvc, err := k.clientset.CoreV1().Services(namespace).Create(ctx, service, metav1.CreateOptions{})
	if err != nil {
		return nil, fmt.Errorf("创建服务失败: %v", err)
	}

	result := map[string]interface{}{
		"name":      createdSvc.Name,
		"namespace": createdSvc.Namespace,
		"type":      createdSvc.Spec.Type,
		"clusterIP": createdSvc.Spec.ClusterIP,
		"uid":       string(createdSvc.UID),
	}

	return result, nil
}

// CreateConfigMap 创建ConfigMap
func (k *K8sClient) CreateConfigMap(namespace string, data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()

	name, ok := data["name"].(string)
	if !ok || name == "" {
		return nil, fmt.Errorf("ConfigMap名称不能为空")
	}

	// 构建ConfigMap对象
	configMap := &v1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Data: make(map[string]string),
	}

	// 添加数据
	if configData, ok := data["data"].(map[string]interface{}); ok {
		for k, v := range configData {
			if str, ok := v.(string); ok {
				configMap.Data[k] = str
			}
		}
	}

	// 创建ConfigMap
	createdCM, err := k.clientset.CoreV1().ConfigMaps(namespace).Create(ctx, configMap, metav1.CreateOptions{})
	if err != nil {
		return nil, fmt.Errorf("创建ConfigMap失败: %v", err)
	}

	result := map[string]interface{}{
		"name":      createdCM.Name,
		"namespace": createdCM.Namespace,
		"dataCount": len(createdCM.Data),
		"uid":       string(createdCM.UID),
	}

	return result, nil
}

// CreateDeployment 创建Deployment
func (k *K8sClient) CreateDeployment(namespace string, data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()

	name, ok := data["name"].(string)
	if !ok || name == "" {
		return nil, fmt.Errorf("Deployment名称不能为空")
	}

	deployment := &appsv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
	}

	createdDeployment, err := k.clientset.AppsV1().Deployments(namespace).Create(ctx, deployment, metav1.CreateOptions{})
	if err != nil {
		return nil, fmt.Errorf("创建Deployment失败: %v", err)
	}

	return map[string]interface{}{
		"name":      createdDeployment.Name,
		"namespace": createdDeployment.Namespace,
		"uid":       string(createdDeployment.UID),
	}, nil
}

// CreateStatefulSet 创建StatefulSet
func (k *K8sClient) CreateStatefulSet(namespace string, data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()

	name, ok := data["name"].(string)
	if !ok || name == "" {
		return nil, fmt.Errorf("StatefulSet名称不能为空")
	}

	statefulSet := &appsv1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
	}

	createdStatefulSet, err := k.clientset.AppsV1().StatefulSets(namespace).Create(ctx, statefulSet, metav1.CreateOptions{})
	if err != nil {
		return nil, fmt.Errorf("创建StatefulSet失败: %v", err)
	}
	return map[string]interface{}{
		"name":      createdStatefulSet.Name,
		"namespace": createdStatefulSet.Namespace,
		"uid":       string(createdStatefulSet.UID),
	}, nil
}

// CreateDaemonSet 创建DaemonSet
func (k *K8sClient) CreateDaemonSet(namespace string, data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()
	name, ok := data["name"].(string)
	if !ok || name == "" {
		return nil, fmt.Errorf("DaemonSet名称不能为空")
	}

	daemonSet := &appsv1.DaemonSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
	}

	createdDaemonSet, err := k.clientset.AppsV1().DaemonSets(namespace).Create(ctx, daemonSet, metav1.CreateOptions{})
	if err != nil {
		return nil, fmt.Errorf("创建DaemonSet失败: %v", err)
	}
	return map[string]interface{}{
		"name":      createdDaemonSet.Name,
		"namespace": createdDaemonSet.Namespace,
		"uid":       string(createdDaemonSet.UID),
	}, nil
}

// CreateJob 创建Job
func (k *K8sClient) CreateJob(namespace string, data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()
	name, ok := data["name"].(string)
	if !ok || name == "" {
		return nil, fmt.Errorf("Job名称不能为空")
	}

	// 解析容器配置
	containers := []v1.Container{}
	if containersData, ok := data["containers"].([]interface{}); ok {
		for _, containerData := range containersData {
			if containerMap, ok := containerData.(map[string]interface{}); ok {
				container := v1.Container{}
				if containerName, ok := containerMap["name"].(string); ok {
					container.Name = containerName
				}
				if image, ok := containerMap["image"].(string); ok {
					container.Image = image
				}
				if command, ok := containerMap["command"].([]interface{}); ok {
					for _, cmd := range command {
						if cmdStr, ok := cmd.(string); ok {
							container.Command = append(container.Command, cmdStr)
						}
					}
				}
				if args, ok := containerMap["args"].([]interface{}); ok {
					for _, arg := range args {
						if argStr, ok := arg.(string); ok {
							container.Args = append(container.Args, argStr)
						}
					}
				}
				containers = append(containers, container)
			}
		}
	}

	// 如果没有提供容器配置，使用默认配置
	if len(containers) == 0 {
		image := "busybox"
		if imageData, ok := data["image"].(string); ok {
			image = imageData
		}

		command := []string{"echo", "hello"}
		if commandData, ok := data["command"].([]interface{}); ok {
			command = []string{}
			for _, cmd := range commandData {
				if cmdStr, ok := cmd.(string); ok {
					command = append(command, cmdStr)
				}
			}
		}

		containers = []v1.Container{
			{
				Name:    "main",
				Image:   image,
				Command: command,
			},
		}
	}

	// 解析其他配置
	restartPolicy := v1.RestartPolicyOnFailure
	if policy, ok := data["restartPolicy"].(string); ok {
		restartPolicy = v1.RestartPolicy(policy)
	}

	var parallelism *int32
	if p, ok := data["parallelism"].(int); ok {
		parallelism = func(i int32) *int32 { return &i }(int32(p))
	}

	var completions *int32
	if c, ok := data["completions"].(int); ok {
		completions = func(i int32) *int32 { return &i }(int32(c))
	}

	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Spec: batchv1.JobSpec{
			Parallelism: parallelism,
			Completions: completions,
			Template: v1.PodTemplateSpec{
				Spec: v1.PodSpec{
					Containers:    containers,
					RestartPolicy: restartPolicy,
				},
			},
		},
	}

	// 添加标签
	if labels, ok := data["labels"].(map[string]interface{}); ok {
		job.ObjectMeta.Labels = make(map[string]string)
		for k, v := range labels {
			if str, ok := v.(string); ok {
				job.ObjectMeta.Labels[k] = str
			}
		}
	}

	createdJob, err := k.clientset.BatchV1().Jobs(namespace).Create(ctx, job, metav1.CreateOptions{})
	if err != nil {
		return nil, fmt.Errorf("创建Job失败: %v", err)
	}
	return map[string]interface{}{
		"name":      createdJob.Name,
		"namespace": createdJob.Namespace,
		"uid":       string(createdJob.UID),
	}, nil
}

// CreateCronJob 创建CronJob
func (k *K8sClient) CreateCronJob(namespace string, data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()
	name, ok := data["name"].(string)
	if !ok || name == "" {
		return nil, fmt.Errorf("CronJob名称不能为空")
	}
	schedule, ok := data["schedule"].(string)
	if !ok || schedule == "" {
		return nil, fmt.Errorf("CronJob调度表达式(schedule)不能为空")
	}

	// 解析容器配置
	containers := []v1.Container{}
	if containersData, ok := data["containers"].([]interface{}); ok {
		for _, containerData := range containersData {
			if containerMap, ok := containerData.(map[string]interface{}); ok {
				container := v1.Container{}
				if containerName, ok := containerMap["name"].(string); ok {
					container.Name = containerName
				}
				if image, ok := containerMap["image"].(string); ok {
					container.Image = image
				}
				if command, ok := containerMap["command"].([]interface{}); ok {
					for _, cmd := range command {
						if cmdStr, ok := cmd.(string); ok {
							container.Command = append(container.Command, cmdStr)
						}
					}
				}
				if args, ok := containerMap["args"].([]interface{}); ok {
					for _, arg := range args {
						if argStr, ok := arg.(string); ok {
							container.Args = append(container.Args, argStr)
						}
					}
				}
				containers = append(containers, container)
			}
		}
	}

	// 如果没有提供容器配置，使用默认配置
	if len(containers) == 0 {
		image := "busybox"
		if imageData, ok := data["image"].(string); ok {
			image = imageData
		}

		command := []string{"echo", "hello"}
		if commandData, ok := data["command"].([]interface{}); ok {
			command = []string{}
			for _, cmd := range commandData {
				if cmdStr, ok := cmd.(string); ok {
					command = append(command, cmdStr)
				}
			}
		}

		containers = []v1.Container{
			{
				Name:    "main",
				Image:   image,
				Command: command,
			},
		}
	}

	// 解析其他配置
	restartPolicy := v1.RestartPolicyOnFailure
	if policy, ok := data["restartPolicy"].(string); ok {
		restartPolicy = v1.RestartPolicy(policy)
	}

	var parallelism *int32
	if p, ok := data["parallelism"].(int); ok {
		parallelism = func(i int32) *int32 { return &i }(int32(p))
	}

	var completions *int32
	if c, ok := data["completions"].(int); ok {
		completions = func(i int32) *int32 { return &i }(int32(c))
	}

	// CronJob 特有配置
	var successfulJobsHistoryLimit *int32
	if limit, ok := data["successfulJobsHistoryLimit"].(int); ok {
		successfulJobsHistoryLimit = func(i int32) *int32 { return &i }(int32(limit))
	}

	var failedJobsHistoryLimit *int32
	if limit, ok := data["failedJobsHistoryLimit"].(int); ok {
		failedJobsHistoryLimit = func(i int32) *int32 { return &i }(int32(limit))
	}

	concurrencyPolicy := batchv1.AllowConcurrent
	if policy, ok := data["concurrencyPolicy"].(string); ok {
		concurrencyPolicy = batchv1.ConcurrencyPolicy(policy)
	}

	cronJob := &batchv1.CronJob{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Spec: batchv1.CronJobSpec{
			Schedule:                   schedule,
			ConcurrencyPolicy:          concurrencyPolicy,
			SuccessfulJobsHistoryLimit: successfulJobsHistoryLimit,
			FailedJobsHistoryLimit:     failedJobsHistoryLimit,
			JobTemplate: batchv1.JobTemplateSpec{
				Spec: batchv1.JobSpec{
					Parallelism: parallelism,
					Completions: completions,
					Template: v1.PodTemplateSpec{
						Spec: v1.PodSpec{
							Containers:    containers,
							RestartPolicy: restartPolicy,
						},
					},
				},
			},
		},
	}

	// 添加标签
	if labels, ok := data["labels"].(map[string]interface{}); ok {
		cronJob.ObjectMeta.Labels = make(map[string]string)
		for k, v := range labels {
			if str, ok := v.(string); ok {
				cronJob.ObjectMeta.Labels[k] = str
			}
		}
	}

	createdCronJob, err := k.clientset.BatchV1().CronJobs(namespace).Create(ctx, cronJob, metav1.CreateOptions{})
	if err != nil {
		return nil, fmt.Errorf("创建CronJob失败: %v", err)
	}
	return map[string]interface{}{
		"name":      createdCronJob.Name,
		"namespace": createdCronJob.Namespace,
		"uid":       string(createdCronJob.UID),
	}, nil
}

// UpdateNamespace 更新命名空间
func (k *K8sClient) UpdateNamespace(data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()

	name, ok := data["name"].(string)

	if !ok || name == "" {
		return nil, fmt.Errorf("命名空间名称不能为空")
	}

	namespace, err := k.clientset.CoreV1().Namespaces().Get(ctx, name, metav1.GetOptions{})

	if err != nil {
		return nil, fmt.Errorf("获取命名空间失败: %v", err)
	}

	namespace.Labels = make(map[string]string)

	if labels, ok := data["labels"].(map[string]interface{}); ok {
		for k, v := range labels {
			if str, ok := v.(string); ok {
				namespace.Labels[k] = str
			}
		}
	}

	updatedNS, err := k.clientset.CoreV1().Namespaces().Update(ctx, namespace, metav1.UpdateOptions{})

	if err != nil {
		return nil, fmt.Errorf("更新命名空间失败: %v", err)
	}

	result := map[string]interface{}{
		"name":   updatedNS.Name,
		"status": updatedNS.Status.Phase,
		"uid":    string(updatedNS.UID),
	}

	return result, nil
}

// UpdateService 更新服务
func (k *K8sClient) UpdateService(data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()

	name, ok := data["name"].(string)

	if !ok || name == "" {
		return nil, fmt.Errorf("服务名称不能为空")
	}

	namespace, ok := data["namespace"].(string)
	if !ok || namespace == "" {
		return nil, fmt.Errorf("命名空间不能为空")
	}

	service, err := k.clientset.CoreV1().Services(namespace).Get(ctx, name, metav1.GetOptions{})

	if err != nil {
		return nil, fmt.Errorf("获取服务失败: %v", err)
	}

	serviceType, _ := data["type"].(string)
	if serviceType == "" {
		serviceType = "ClusterIP"
	}
	service.Spec.Type = v1.ServiceType(serviceType)

	updatedSvc, err := k.clientset.CoreV1().Services(namespace).Update(ctx, service, metav1.UpdateOptions{})

	if err != nil {
		return nil, fmt.Errorf("更新服务失败: %v", err)
	}

	result := map[string]interface{}{
		"name":      updatedSvc.Name,
		"namespace": updatedSvc.Namespace,
		"type":      updatedSvc.Spec.Type,
		"clusterIP": updatedSvc.Spec.ClusterIP,
		"uid":       string(updatedSvc.UID),
	}

	return result, nil
}

// UpdateConfigMap 更新ConfigMap
func (k *K8sClient) UpdateConfigMap(data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()

	name, ok := data["name"].(string)

	if !ok || name == "" {
		return nil, fmt.Errorf("ConfigMap名称不能为空")
	}

	namespace, ok := data["namespace"].(string)

	if !ok || namespace == "" {
		return nil, fmt.Errorf("命名空间不能为空")
	}

	configMap, err := k.clientset.CoreV1().ConfigMaps(namespace).Get(ctx, name, metav1.GetOptions{})

	if err != nil {
		return nil, fmt.Errorf("获取ConfigMap失败: %v", err)
	}

	configMap.Data = make(map[string]string)

	if data, ok := data["data"].(map[string]interface{}); ok {
		for k, v := range data {
			if str, ok := v.(string); ok {
				configMap.Data[k] = str
			}
		}
	}

	updatedCM, err := k.clientset.CoreV1().ConfigMaps(namespace).Update(ctx, configMap, metav1.UpdateOptions{})

	if err != nil {
		return nil, fmt.Errorf("更新ConfigMap失败: %v", err)
	}

	result := map[string]interface{}{
		"name":      updatedCM.Name,
		"namespace": updatedCM.Namespace,
		"dataCount": len(updatedCM.Data),
		"uid":       string(updatedCM.UID),
	}

	return result, nil
}

// UpdateDeployment 更新Deployment
func (k *K8sClient) UpdateDeployment(data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()

	name, ok := data["name"].(string)
	replicas, ok := data["replicas"].(int)

	if !ok || name == "" {
		return nil, fmt.Errorf("Deployment名称不能为空")
	}

	namespace, ok := data["namespace"].(string)

	if !ok || namespace == "" {
		return nil, fmt.Errorf("命名空间不能为空")
	}

	deployment, err := k.clientset.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})

	if err != nil {
		return nil, fmt.Errorf("获取Deployment失败: %v", err)
	}

	deployment.Spec.Replicas = func(i int32) *int32 { return &i }(int32(replicas))

	updatedDeployment, err := k.clientset.AppsV1().Deployments(namespace).Update(ctx, deployment, metav1.UpdateOptions{})

	if err != nil {
		return nil, fmt.Errorf("更新Deployment失败: %v", err)
	}

	result := map[string]interface{}{
		"name":      updatedDeployment.Name,
		"namespace": updatedDeployment.Namespace,
		"uid":       string(updatedDeployment.UID),
	}

	return result, nil
}

// UpdateStatefulSet 更新StatefulSet
func (k *K8sClient) UpdateStatefulSet(data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()

	name, ok := data["name"].(string)

	if !ok || name == "" {
		return nil, fmt.Errorf("StatefulSet名称不能为空")
	}

	namespace, ok := data["namespace"].(string)

	if !ok || namespace == "" {
		return nil, fmt.Errorf("命名空间不能为空")
	}

	statefulSet, err := k.clientset.AppsV1().StatefulSets(namespace).Get(ctx, name, metav1.GetOptions{})

	if err != nil {
		return nil, fmt.Errorf("获取StatefulSet失败: %v", err)
	}

	updatedStatefulSet, err := k.clientset.AppsV1().StatefulSets(namespace).Update(ctx, statefulSet, metav1.UpdateOptions{})

	if err != nil {
		return nil, fmt.Errorf("更新StatefulSet失败: %v", err)
	}

	result := map[string]interface{}{
		"name":      updatedStatefulSet.Name,
		"namespace": updatedStatefulSet.Namespace,
		"uid":       string(updatedStatefulSet.UID),
	}

	return result, nil
}

// UpdateDaemonSet 更新DaemonSet
func (k *K8sClient) UpdateDaemonSet(data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()

	name, ok := data["name"].(string)

	if !ok || name == "" {
		return nil, fmt.Errorf("DaemonSet名称不能为空")
	}

	namespace, ok := data["namespace"].(string)

	if !ok || namespace == "" {
		return nil, fmt.Errorf("命名空间不能为空")
	}

	daemonSet, err := k.clientset.AppsV1().DaemonSets(namespace).Get(ctx, name, metav1.GetOptions{})

	if err != nil {
		return nil, fmt.Errorf("获取DaemonSet失败: %v", err)
	}

	// DaemonSet 不支持 Replicas 字段，可以更新其他字段如 labels
	if labels, ok := data["labels"].(map[string]interface{}); ok {
		if daemonSet.Labels == nil {
			daemonSet.Labels = make(map[string]string)
		}
		for k, v := range labels {
			if str, ok := v.(string); ok {
				daemonSet.Labels[k] = str
			}
		}
	}

	updatedDaemonSet, err := k.clientset.AppsV1().DaemonSets(namespace).Update(ctx, daemonSet, metav1.UpdateOptions{})

	if err != nil {
		return nil, fmt.Errorf("更新DaemonSet失败: %v", err)
	}

	result := map[string]interface{}{
		"name":      updatedDaemonSet.Name,
		"namespace": updatedDaemonSet.Namespace,
		"uid":       string(updatedDaemonSet.UID),
	}

	return result, nil
}

// UpdateJob 更新Job
func (k *K8sClient) UpdateJob(data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()

	name, ok := data["name"].(string)
	if !ok || name == "" {
		return nil, fmt.Errorf("Job名称不能为空")
	}

	parallelism, ok := data["parallelism"].(int)
	if !ok {
		// 如果没有提供parallelism，使用默认值1
		parallelism = 1
	}

	namespace, ok := data["namespace"].(string)

	if !ok || namespace == "" {
		return nil, fmt.Errorf("命名空间不能为空")
	}

	job, err := k.clientset.BatchV1().Jobs(namespace).Get(ctx, name, metav1.GetOptions{})

	if err != nil {
		return nil, fmt.Errorf("获取Job失败: %v", err)
	}

	job.Spec.Parallelism = func(i int32) *int32 { return &i }(int32(parallelism))

	updatedJob, err := k.clientset.BatchV1().Jobs(namespace).Update(ctx, job, metav1.UpdateOptions{})

	if err != nil {
		return nil, fmt.Errorf("更新Job失败: %v", err)
	}

	result := map[string]interface{}{
		"name":      updatedJob.Name,
		"namespace": updatedJob.Namespace,
		"uid":       string(updatedJob.UID),
	}

	return result, nil
}

// UpdateCronJob 更新CronJob
func (k *K8sClient) UpdateCronJob(data map[string]interface{}) (map[string]interface{}, error) {
	ctx := context.Background()

	name, ok := data["name"].(string)

	if !ok || name == "" {
		return nil, fmt.Errorf("CronJob名称不能为空")
	}

	namespace, ok := data["namespace"].(string)

	if !ok || namespace == "" {
		return nil, fmt.Errorf("命名空间不能为空")
	}

	cronJob, err := k.clientset.BatchV1().CronJobs(namespace).Get(ctx, name, metav1.GetOptions{})

	if err != nil {
		return nil, fmt.Errorf("获取CronJob失败: %v", err)
	}

	updatedCronJob, err := k.clientset.BatchV1().CronJobs(namespace).Update(ctx, cronJob, metav1.UpdateOptions{})

	if err != nil {
		return nil, fmt.Errorf("更新CronJob失败: %v", err)
	}

	result := map[string]interface{}{
		"name":      updatedCronJob.Name,
		"namespace": updatedCronJob.Namespace,
		"uid":       string(updatedCronJob.UID),
	}

	return result, nil
}

// DeleteNamespace 删除命名空间
func (k *K8sClient) DeleteNamespace(name string) (map[string]interface{}, error) {
	ctx := context.Background()

	err := k.clientset.CoreV1().Namespaces().Delete(ctx, name, metav1.DeleteOptions{})

	if err != nil {
		return nil, fmt.Errorf("删除命名空间失败: %v", err)
	}

	result := map[string]interface{}{
		"name": name,
	}

	return result, nil
}

// DeleteService 删除服务
func (k *K8sClient) DeleteService(namespace string, name string) (map[string]interface{}, error) {
	ctx := context.Background()

	err := k.clientset.CoreV1().Services(namespace).Delete(ctx, name, metav1.DeleteOptions{})

	if err != nil {
		return nil, fmt.Errorf("删除服务失败: %v", err)
	}

	result := map[string]interface{}{
		"name": name,
	}

	return result, nil
}

// DeleteConfigMap 删除ConfigMap
func (k *K8sClient) DeleteConfigMap(namespace string, name string) (map[string]interface{}, error) {
	ctx := context.Background()

	err := k.clientset.CoreV1().ConfigMaps(namespace).Delete(ctx, name, metav1.DeleteOptions{})

	if err != nil {
		return nil, fmt.Errorf("删除ConfigMap失败: %v", err)
	}

	result := map[string]interface{}{
		"name": name,
	}

	return result, nil
}

// DeleteJob 删除Job
func (k *K8sClient) DeleteJob(namespace string, name string) (map[string]interface{}, error) {
	ctx := context.Background()

	err := k.clientset.BatchV1().Jobs(namespace).Delete(ctx, name, metav1.DeleteOptions{})

	if err != nil {
		return nil, fmt.Errorf("删除Job失败: %v", err)
	}

	result := map[string]interface{}{
		"name": name,
	}

	return result, nil
}

// DeleteCronJob 删除CronJob
func (k *K8sClient) DeleteCronJob(namespace string, name string) (map[string]interface{}, error) {
	ctx := context.Background()

	err := k.clientset.BatchV1().CronJobs(namespace).Delete(ctx, name, metav1.DeleteOptions{})

	if err != nil {
		return nil, fmt.Errorf("删除CronJob失败: %v", err)
	}

	result := map[string]interface{}{
		"name": name,
	}

	return result, nil
}

// DeleteDeployment 删除Deployment
func (k *K8sClient) DeleteDeployment(namespace string, name string) (map[string]interface{}, error) {
	ctx := context.Background()

	err := k.clientset.AppsV1().Deployments(namespace).Delete(ctx, name, metav1.DeleteOptions{})

	if err != nil {
		return nil, fmt.Errorf("删除Deployment失败: %v", err)
	}

	result := map[string]interface{}{
		"name": name,
	}

	return result, nil
}

// DeleteStatefulSet 删除StatefulSet
func (k *K8sClient) DeleteStatefulSet(namespace string, name string) (map[string]interface{}, error) {
	ctx := context.Background()

	err := k.clientset.AppsV1().StatefulSets(namespace).Delete(ctx, name, metav1.DeleteOptions{})

	if err != nil {
		return nil, fmt.Errorf("删除StatefulSet失败: %v", err)
	}

	result := map[string]interface{}{
		"name": name,
	}

	return result, nil
}

// DeleteDaemonSet 删除DaemonSet
func (k *K8sClient) DeleteDaemonSet(namespace string, name string) (map[string]interface{}, error) {
	ctx := context.Background()

	err := k.clientset.AppsV1().DaemonSets(namespace).Delete(ctx, name, metav1.DeleteOptions{})

	if err != nil {
		return nil, fmt.Errorf("删除DaemonSet失败: %v", err)
	}

	result := map[string]interface{}{
		"name": name,
	}

	return result, nil
}

// GetEvents 获取事件列表
func (k *K8sClient) GetEvents(namespace string, fieldSelector string, limit int) (map[string]interface{}, error) {
	ctx := context.Background()

	listOptions := metav1.ListOptions{
		Limit: int64(limit),
	}

	if fieldSelector != "" {
		listOptions.FieldSelector = fieldSelector
	}

	var eventList *v1.EventList
	var err error

	if namespace == "" {
		// 获取所有命名空间的事件
		eventList, err = k.clientset.CoreV1().Events("").List(ctx, listOptions)
	} else {
		// 获取指定命名空间的事件
		eventList, err = k.clientset.CoreV1().Events(namespace).List(ctx, listOptions)
	}

	if err != nil {
		return nil, fmt.Errorf("获取事件列表失败: %v", err)
	}

	result := map[string]interface{}{
		"items": eventList.Items,
		"metadata": map[string]interface{}{
			"continue": eventList.Continue,
		},
	}

	return result, nil
}

// DeletePods 批量删除Pod
func (k *K8sClient) DeletePods(namespace string, podNames []string) (map[string]interface{}, error) {
	ctx := context.Background()

	deleteOptions := metav1.DeleteOptions{
		GracePeriodSeconds: func(i int64) *int64 { return &i }(30), // 30秒优雅删除
	}

	results := make(map[string]interface{})
	successCount := 0
	failedCount := 0
	errors := make([]string, 0)

	for _, podName := range podNames {
		err := k.clientset.CoreV1().Pods(namespace).Delete(ctx, podName, deleteOptions)
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("删除Pod %s失败: %v", podName, err))
		} else {
			successCount++
		}
	}

	results["success_count"] = successCount
	results["failed_count"] = failedCount
	results["total_count"] = len(podNames)
	results["errors"] = errors

	if failedCount > 0 {
		return results, fmt.Errorf("批量删除Pod部分失败，成功: %d, 失败: %d", successCount, failedCount)
	}

	return results, nil
}

// BatchCreateResources 批量创建资源
func (k *K8sClient) BatchCreateResources(resourceType string, resources []map[string]interface{}) (map[string]interface{}, error) {
	results := make(map[string]interface{})
	successCount := 0
	failedCount := 0
	errors := make([]string, 0)
	createdResources := make([]map[string]interface{}, 0)

	for i, resource := range resources {
		var result map[string]interface{}
		var err error

		switch resourceType {
		case "namespace":
			result, err = k.CreateNamespace(resource)
		case "service":
			namespace, ok := resource["namespace"].(string)
			if !ok {
				err = fmt.Errorf("资源 %d: 缺少namespace字段", i)
			} else {
				result, err = k.CreateService(namespace, resource)
			}
		case "configmap":
			namespace, ok := resource["namespace"].(string)
			if !ok {
				err = fmt.Errorf("资源 %d: 缺少namespace字段", i)
			} else {
				result, err = k.CreateConfigMap(namespace, resource)
			}
		default:
			err = fmt.Errorf("不支持的资源类型: %s", resourceType)
		}

		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("创建资源 %d 失败: %v", i, err))
		} else {
			successCount++
			createdResources = append(createdResources, result)
		}
	}

	results["success_count"] = successCount
	results["failed_count"] = failedCount
	results["total_count"] = len(resources)
	results["errors"] = errors
	results["created_resources"] = createdResources

	if failedCount > 0 {
		return results, fmt.Errorf("批量创建资源部分失败，成功: %d, 失败: %d", successCount, failedCount)
	}

	return results, nil
}

// BatchUpdateResources 批量更新资源
func (k *K8sClient) BatchUpdateResources(resourceType string, resources []map[string]interface{}) (map[string]interface{}, error) {
	results := make(map[string]interface{})
	successCount := 0
	failedCount := 0
	errors := make([]string, 0)
	updatedResources := make([]map[string]interface{}, 0)

	for i, resource := range resources {
		var result map[string]interface{}
		var err error

		switch resourceType {
		case "namespace":
			result, err = k.UpdateNamespace(resource)
		case "service":
			result, err = k.UpdateService(resource)
		case "configmap":
			result, err = k.UpdateConfigMap(resource)
		}
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("更新资源 %d 失败: %v", i, err))
		} else {
			successCount++
			updatedResources = append(updatedResources, result)
		}
	}

	results["success_count"] = successCount
	results["failed_count"] = failedCount
	results["total_count"] = len(resources)
	results["errors"] = errors
	results["updated_resources"] = updatedResources

	if failedCount > 0 {
		return results, fmt.Errorf("批量更新资源部分失败，成功: %d, 失败: %d", successCount, failedCount)
	}

	return results, nil
}

// BatchDeleteResources 批量删除资源
func (k *K8sClient) BatchDeleteResources(namespace string, resourceType string, resourceNames []string) (map[string]interface{}, error) {
	results := make(map[string]interface{})
	successCount := 0
	failedCount := 0
	errors := make([]string, 0)
	deletedResources := make([]map[string]interface{}, 0)
	for i, resourceName := range resourceNames {
		var result map[string]interface{}
		var err error

		switch resourceType {
		case "namespace":
			result, err = k.DeleteNamespace(resourceName)
		case "service":
			result, err = k.DeleteService(namespace, resourceName)
		case "configmap":
			result, err = k.DeleteConfigMap(namespace, resourceName)
		case "deployment":
			result, err = k.DeleteDeployment(namespace, resourceName)
		case "statefulset":
			result, err = k.DeleteStatefulSet(namespace, resourceName)
		case "daemonset":
			result, err = k.DeleteDaemonSet(namespace, resourceName)
		case "job":
			result, err = k.DeleteJob(namespace, resourceName)
		case "cronjob":
			result, err = k.DeleteCronJob(namespace, resourceName)
		default:
			err = fmt.Errorf("不支持的资源类型: %s", resourceType)
		}
		if err != nil {
			failedCount++
			errors = append(errors, fmt.Sprintf("删除资源 %d 失败: %v", i, err))
		} else {
			successCount++
			deletedResources = append(deletedResources, result)
		}
	}

	results["success_count"] = successCount
	results["failed_count"] = failedCount
	results["total_count"] = len(resourceNames)
	results["errors"] = errors
	results["deleted_resources"] = deletedResources

	if failedCount > 0 {
		return results, fmt.Errorf("批量删除资源部分失败，成功: %d, 失败: %d", successCount, failedCount)
	}

	return results, nil
}

// CreateWorkload 创建Workload
func (k *K8sClient) CreateWorkload(namespace string, workloadType string, data map[string]interface{}) (map[string]interface{}, error) {
	name, ok := data["name"].(string)
	if !ok || name == "" {
		return nil, fmt.Errorf("Workload名称不能为空")
	}

	switch workloadType {
	case "deployment":
		return k.CreateDeployment(namespace, data)
	case "statefulset":
		return k.CreateStatefulSet(namespace, data)
	case "daemonset":
		return k.CreateDaemonSet(namespace, data)
	case "job":
		return k.CreateJob(namespace, data)
	case "cronjob":
		return k.CreateCronJob(namespace, data)
	}

	return nil, fmt.Errorf("不支持的Workload类型: %s", workloadType)
}

// UpdateWorkload 更新Workload
func (k *K8sClient) UpdateWorkload(namespace string, name string, data map[string]interface{}) (map[string]interface{}, error) {
	workloadType, ok := data["type"].(string)
	if !ok || workloadType == "" {
		return nil, fmt.Errorf("Workload类型不能为空")
	}

	switch workloadType {
	case "deployment":
		return k.UpdateDeployment(data)
	case "statefulset":
		return k.UpdateStatefulSet(data)
	case "daemonset":
		return k.UpdateDaemonSet(data)
	case "job":
		return k.UpdateJob(data)
	case "cronjob":
		return k.UpdateCronJob(data)
	}

	return nil, fmt.Errorf("不支持的Workload类型: %s", workloadType)
}

// DeleteWorkload 删除Workload
func (k *K8sClient) DeleteWorkload(namespace string, name string, workloadType string) (map[string]interface{}, error) {
	var err error
	switch workloadType {
	case "deployment":
		_, err = k.DeleteDeployment(namespace, name)
	case "statefulset":
		_, err = k.DeleteStatefulSet(namespace, name)
	case "daemonset":
		_, err = k.DeleteDaemonSet(namespace, name)
	case "job":
		_, err = k.DeleteJob(namespace, name)
	case "cronjob":
		_, err = k.DeleteCronJob(namespace, name)
	default:
		return nil, fmt.Errorf("不支持的工作负载类型: %s", workloadType)
	}

	if err != nil {
		return nil, err
	}

	result := map[string]interface{}{
		"name": name,
		"type": workloadType,
	}

	return result, nil
}

// ScaleWorkload 扩缩容Workload
func (k *K8sClient) ScaleWorkload(namespace string, name string, replicas int, workloadType string) (map[string]interface{}, error) {
	ctx := context.Background()

	scale := &autoscalingv1.Scale{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
		},
		Spec: autoscalingv1.ScaleSpec{
			Replicas: int32(replicas),
		},
	}

	var err error
	switch workloadType {
	case "deployment":
		_, err = k.clientset.AppsV1().Deployments(namespace).UpdateScale(ctx, name, scale, metav1.UpdateOptions{})
	case "statefulset":
		_, err = k.clientset.AppsV1().StatefulSets(namespace).UpdateScale(ctx, name, scale, metav1.UpdateOptions{})
	case "replicaset":
		_, err = k.clientset.AppsV1().ReplicaSets(namespace).UpdateScale(ctx, name, scale, metav1.UpdateOptions{})
	default:
		return nil, fmt.Errorf("不支持的工作负载类型扩缩容: %s", workloadType)
	}

	if err != nil {
		return nil, fmt.Errorf("扩缩容%s失败: %v", workloadType, err)
	}

	result := map[string]interface{}{
		"name":     name,
		"replicas": replicas,
		"type":     workloadType,
	}

	return result, nil
}

// RestartWorkload 重启Workload
func (k *K8sClient) RestartWorkload(namespace string, name string, workloadType string) (map[string]interface{}, error) {
	ctx := context.Background()

	var err error
	switch workloadType {
	case "deployment":
		// 对于 Deployment，通过更新 annotation 来触发重启
		deployment, getErr := k.clientset.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
		if getErr != nil {
			return nil, fmt.Errorf("获取Deployment失败: %v", getErr)
		}

		// 添加重启注解
		if deployment.Spec.Template.Annotations == nil {
			deployment.Spec.Template.Annotations = make(map[string]string)
		}
		deployment.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"] = fmt.Sprintf("%d", time.Now().Unix())

		_, err = k.clientset.AppsV1().Deployments(namespace).Update(ctx, deployment, metav1.UpdateOptions{})
	case "statefulset":
		// 对于 StatefulSet，通过更新 annotation 来触发重启
		statefulset, getErr := k.clientset.AppsV1().StatefulSets(namespace).Get(ctx, name, metav1.GetOptions{})
		if getErr != nil {
			return nil, fmt.Errorf("获取StatefulSet失败: %v", getErr)
		}

		// 添加重启注解
		if statefulset.Spec.Template.Annotations == nil {
			statefulset.Spec.Template.Annotations = make(map[string]string)
		}
		statefulset.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"] = fmt.Sprintf("%d", time.Now().Unix())

		_, err = k.clientset.AppsV1().StatefulSets(namespace).Update(ctx, statefulset, metav1.UpdateOptions{})
	case "daemonset":
		// 对于 DaemonSet，通过更新 annotation 来触发重启
		daemonset, getErr := k.clientset.AppsV1().DaemonSets(namespace).Get(ctx, name, metav1.GetOptions{})
		if getErr != nil {
			return nil, fmt.Errorf("获取DaemonSet失败: %v", getErr)
		}

		// 添加重启注解
		if daemonset.Spec.Template.Annotations == nil {
			daemonset.Spec.Template.Annotations = make(map[string]string)
		}
		daemonset.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"] = fmt.Sprintf("%d", time.Now().Unix())

		_, err = k.clientset.AppsV1().DaemonSets(namespace).Update(ctx, daemonset, metav1.UpdateOptions{})
	case "job":
		// Job 不支持重启，需要删除后重新创建
		err = k.clientset.BatchV1().Jobs(namespace).Delete(ctx, name, metav1.DeleteOptions{})
	case "cronjob":
		// CronJob 不支持重启，只能删除相关的 Job
		err = fmt.Errorf("CronJob不支持重启操作，请删除相关的Job")
	default:
		return nil, fmt.Errorf("不支持的工作负载类型重启: %s", workloadType)
	}

	if err != nil {
		return nil, fmt.Errorf("重启%s失败: %v", workloadType, err)
	}

	result := map[string]interface{}{
		"name": name,
		"type": workloadType,
	}

	return result, nil
}

// GetWorkloads
func (k *K8sClient) GetWorkloads(namespace string, workloadType string) (map[string]interface{}, error) {
	ctx := context.Background()

	var err error
	var workloads []map[string]interface{}
	switch workloadType {
	case "deployment":
		_, err = k.clientset.AppsV1().Deployments(namespace).List(ctx, metav1.ListOptions{})
	case "statefulset":
		_, err = k.clientset.AppsV1().StatefulSets(namespace).List(ctx, metav1.ListOptions{})
	}

	if err != nil {
		return nil, fmt.Errorf("获取工作负载失败: %v", err)
	}

	result := map[string]interface{}{
		"workloads": workloads,
	}

	return result, nil
}
