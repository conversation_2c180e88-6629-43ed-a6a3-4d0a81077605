package controllers

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gorilla/mux"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"imaojia.com/k8slog/config"
	"imaojia.com/k8slog/model"
	"imaojia.com/k8slog/services"
	"imaojia.com/k8slog/utils"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
)

// EnhancedLogController 增强的日志控制器
type EnhancedLogController struct {
	db         *gorm.DB
	cfg        *config.Config
	log        *logrus.Logger
	logService *services.EnhancedLogService
	upgrader   websocket.Upgrader
}

// NewEnhancedLogController 创建增强的日志控制器
func NewEnhancedLogController(db *gorm.DB, cfg *config.Config, log *logrus.Logger) *EnhancedLogController {
	return &EnhancedLogController{
		db:         db,
		cfg:        cfg,
		log:        log,
		logService: services.NewEnhancedLogService(db, log),
		upgrader: websocket.Upgrader{
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
			CheckOrigin:     func(r *http.Request) bool { return true },
		},
	}
}

// GetLogs 获取日志
func (c *EnhancedLogController) GetLogs(w http.ResponseWriter, r *http.Request) {
	// 解析查询参数
	query, err := c.parseLogQuery(r)
	if err != nil {
		c.writeErrorResponse(w, http.StatusBadRequest, "参数解析失败", err)
		return
	}

	// 获取Kubernetes客户端
	clientset, err := c.getKubernetesClient(query.ClusterID)
	if err != nil {
		c.writeErrorResponse(w, http.StatusInternalServerError, "创建Kubernetes客户端失败", err)
		return
	}

	// 获取日志
	result, err := c.logService.GetLogs(r.Context(), clientset, *query)
	if err != nil {
		c.writeErrorResponse(w, http.StatusInternalServerError, "获取日志失败", err)
		return
	}

	c.writeSuccessResponse(w, result)
}

// GetLogStatistics 获取日志统计
func (c *EnhancedLogController) GetLogStatistics(w http.ResponseWriter, r *http.Request) {
	// 解析查询参数
	query, err := c.parseLogQuery(r)
	if err != nil {
		c.writeErrorResponse(w, http.StatusBadRequest, "参数解析失败", err)
		return
	}

	// 获取Kubernetes客户端
	clientset, err := c.getKubernetesClient(query.ClusterID)
	if err != nil {
		c.writeErrorResponse(w, http.StatusInternalServerError, "创建Kubernetes客户端失败", err)
		return
	}

	// 获取日志统计
	result, err := c.logService.GetLogs(r.Context(), clientset, *query)
	if err != nil {
		c.writeErrorResponse(w, http.StatusInternalServerError, "获取日志统计失败", err)
		return
	}

	c.writeSuccessResponse(w, result.Statistics)
}

// ExportLogs 导出日志
func (c *EnhancedLogController) ExportLogs(w http.ResponseWriter, r *http.Request) {
	// 解析查询参数
	query, err := c.parseLogQuery(r)
	if err != nil {
		c.writeErrorResponse(w, http.StatusBadRequest, "参数解析失败", err)
		return
	}

	format := r.URL.Query().Get("format")
	if format == "" {
		format = "json"
	}

	// 获取Kubernetes客户端
	clientset, err := c.getKubernetesClient(query.ClusterID)
	if err != nil {
		c.writeErrorResponse(w, http.StatusInternalServerError, "创建Kubernetes客户端失败", err)
		return
	}

	// 导出日志
	data, err := c.logService.ExportLogs(r.Context(), clientset, *query, format)
	if err != nil {
		c.writeErrorResponse(w, http.StatusInternalServerError, "导出日志失败", err)
		return
	}

	// 设置响应头
	filename := fmt.Sprintf("logs_%s.%s", time.Now().Format("20060102_150405"), format)
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	
	switch format {
	case "json":
		w.Header().Set("Content-Type", "application/json")
	case "csv":
		w.Header().Set("Content-Type", "text/csv")
	case "txt":
		w.Header().Set("Content-Type", "text/plain")
	}

	w.Write(data)
}

// StreamLogs WebSocket实时日志流
func (c *EnhancedLogController) StreamLogs(w http.ResponseWriter, r *http.Request) {
	// 升级为WebSocket连接
	conn, err := c.upgrader.Upgrade(w, r, nil)
	if err != nil {
		c.log.WithError(err).Error("WebSocket升级失败")
		return
	}
	defer conn.Close()

	// 解析查询参数
	query, err := c.parseLogQuery(r)
	if err != nil {
		conn.WriteMessage(websocket.TextMessage, []byte(fmt.Sprintf("参数解析失败: %v", err)))
		return
	}

	// 设置为follow模式
	query.Follow = true

	// 获取Kubernetes客户端
	clientset, err := c.getKubernetesClient(query.ClusterID)
	if err != nil {
		conn.WriteMessage(websocket.TextMessage, []byte(fmt.Sprintf("创建Kubernetes客户端失败: %v", err)))
		return
	}

	// 创建上下文
	ctx, cancel := context.WithCancel(r.Context())
	defer cancel()

	// 启动日志流处理
	c.handleLogStream(ctx, conn, clientset, *query)
}

// handleLogStream 处理日志流
func (c *EnhancedLogController) handleLogStream(ctx context.Context, conn *websocket.Conn, clientset *kubernetes.Clientset, query services.LogQuery) {
	// 设置WebSocket参数
	conn.SetReadLimit(512 * 1024)
	conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	conn.SetPongHandler(func(string) error {
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	// 启动心跳
	go c.startHeartbeat(ctx, conn)

	// 监听客户端消息
	go c.handleClientMessages(ctx, conn)

	// 获取并流式传输日志
	c.streamLogsToClient(ctx, conn, clientset, query)
}

// startHeartbeat 启动心跳
func (c *EnhancedLogController) startHeartbeat(ctx context.Context, conn *websocket.Conn) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if err := conn.WriteControl(websocket.PingMessage, []byte{}, time.Now().Add(10*time.Second)); err != nil {
				c.log.WithError(err).Error("发送心跳失败")
				return
			}
		case <-ctx.Done():
			return
		}
	}
}

// handleClientMessages 处理客户端消息
func (c *EnhancedLogController) handleClientMessages(ctx context.Context, conn *websocket.Conn) {
	for {
		select {
		case <-ctx.Done():
			return
		default:
			_, message, err := conn.ReadMessage()
			if err != nil {
				c.log.WithError(err).Error("读取WebSocket消息失败")
				return
			}

			if string(message) == "disconnect" {
				return
			}
		}
	}
}

// streamLogsToClient 向客户端流式传输日志
func (c *EnhancedLogController) streamLogsToClient(ctx context.Context, conn *websocket.Conn, clientset *kubernetes.Clientset, query services.LogQuery) {
	// 这里应该实现实时日志流，简化实现
	// 实际项目中需要使用Kubernetes的日志流API
	
	// 模拟发送一些日志
	for i := 0; i < 10; i++ {
		select {
		case <-ctx.Done():
			return
		default:
			logEntry := services.LogEntry{
				Timestamp: time.Now(),
				Level:     "INFO",
				Message:   fmt.Sprintf("模拟日志消息 %d", i+1),
				Source: services.LogSource{
					Namespace: query.Namespace,
					Pod:       query.Pod,
				},
			}

			data, _ := json.Marshal(logEntry)
			if err := conn.WriteMessage(websocket.TextMessage, data); err != nil {
				c.log.WithError(err).Error("发送日志消息失败")
				return
			}

			time.Sleep(1 * time.Second)
		}
	}
}

// parseLogQuery 解析日志查询参数
func (c *EnhancedLogController) parseLogQuery(r *http.Request) (*services.LogQuery, error) {
	query := &services.LogQuery{}

	// 解析集群ID
	if clusterIDStr := r.URL.Query().Get("cluster_id"); clusterIDStr != "" {
		clusterID, err := strconv.ParseUint(clusterIDStr, 10, 32)
		if err != nil {
			return nil, fmt.Errorf("无效的集群ID: %v", err)
		}
		query.ClusterID = uint(clusterID)
	}

	// 基本参数
	query.Namespace = r.URL.Query().Get("namespace")
	query.Pod = r.URL.Query().Get("pod")
	query.Container = r.URL.Query().Get("container")
	query.Level = r.URL.Query().Get("level")

	// 时间参数
	if startTimeStr := r.URL.Query().Get("start_time"); startTimeStr != "" {
		if startTime, err := time.Parse(time.RFC3339, startTimeStr); err == nil {
			query.StartTime = &startTime
		}
	}

	if endTimeStr := r.URL.Query().Get("end_time"); endTimeStr != "" {
		if endTime, err := time.Parse(time.RFC3339, endTimeStr); err == nil {
			query.EndTime = &endTime
		}
	}

	// TailLines参数
	if tailLinesStr := r.URL.Query().Get("tail_lines"); tailLinesStr != "" {
		if tailLines, err := strconv.ParseInt(tailLinesStr, 10, 64); err == nil {
			query.TailLines = tailLines
		}
	} else {
		query.TailLines = 100 // 默认值
	}

	// SinceSeconds参数
	if sinceSecondsStr := r.URL.Query().Get("since_seconds"); sinceSecondsStr != "" {
		if sinceSeconds, err := strconv.ParseInt(sinceSecondsStr, 10, 64); err == nil {
			query.SinceSeconds = &sinceSeconds
		}
	}

	// 关键词搜索
	if keywordsStr := r.URL.Query().Get("keywords"); keywordsStr != "" {
		query.Keywords = strings.Split(keywordsStr, ",")
	}

	// Previous参数
	query.Previous = r.URL.Query().Get("previous") == "true"

	return query, nil
}

// getKubernetesClient 获取Kubernetes客户端
func (c *EnhancedLogController) getKubernetesClient(clusterID uint) (*kubernetes.Clientset, error) {
	// 查询集群配置
	var cluster model.CmdbKubernetesCluster
	if err := c.db.First(&cluster, clusterID).Error; err != nil {
		return nil, fmt.Errorf("集群不存在: %w", err)
	}

	// 解密配置
	ef, err := utils.NewEncryptedField(c.cfg.FernetKeys, c.cfg.HKDFSalt, c.cfg.HKDFInfo)
	if err != nil {
		return nil, fmt.Errorf("创建加密字段失败: %w", err)
	}

	configStr, err := cluster.ConfigString()
	if err != nil {
		return nil, fmt.Errorf("获取配置字符串失败: %w", err)
	}

	decryptedData, err := ef.Decrypt([]byte(configStr))
	if err != nil {
		return nil, fmt.Errorf("解密配置失败: %w", err)
	}

	var decryConfig map[string]interface{}
	if err := json.Unmarshal(decryptedData, &decryConfig); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	// 创建Kubernetes客户端
	k8sConfig, err := clientcmd.NewClientConfigFromBytes([]byte(decryConfig["kubeconfig"].(string)))
	if err != nil {
		return nil, fmt.Errorf("解析Kubernetes配置失败: %w", err)
	}

	restConfig, err := k8sConfig.ClientConfig()
	if err != nil {
		return nil, fmt.Errorf("创建REST配置失败: %w", err)
	}

	clientset, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		return nil, fmt.Errorf("创建Kubernetes客户端失败: %w", err)
	}

	return clientset, nil
}

// writeSuccessResponse 写入成功响应
func (c *EnhancedLogController) writeSuccessResponse(w http.ResponseWriter, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	response := map[string]interface{}{
		"code": 20000,
		"data": data,
	}
	json.NewEncoder(w).Encode(response)
}

// writeErrorResponse 写入错误响应
func (c *EnhancedLogController) writeErrorResponse(w http.ResponseWriter, statusCode int, message string, err error) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	
	response := map[string]interface{}{
		"code":    statusCode * 100, // 转换为业务错误码
		"message": message,
	}
	
	if err != nil {
		response["error"] = err.Error()
		c.log.WithError(err).Error(message)
	}
	
	json.NewEncoder(w).Encode(response)
}
