package routes

import (
	"net/http"

	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"imaojia.com/k8slog/config"
	"imaojia.com/k8slog/controllers"
	"imaojia.com/k8slog/middleware"
)

// SetupEnhancedRoutes 设置增强的路由
func SetupEnhancedRoutes(db *gorm.DB, cfg *config.Config, log *logrus.Logger) *mux.Router {
	router := mux.NewRouter()

	// 创建增强的日志控制器
	logController := controllers.NewEnhancedLogController(db, cfg, log)

	// 中间件
	router.Use(corsMiddleware)
	router.Use(loggingMiddleware(log))

	// API路由组
	api := router.PathPrefix("/api/v1").Subrouter()
	api.Use(middleware.AuthMiddleware)

	// ============== 日志相关路由 ==============
	
	// 获取日志
	api.HandleFunc("/logs", logController.GetLogs).Methods("GET")
	
	// 获取日志统计
	api.HandleFunc("/logs/statistics", logController.GetLogStatistics).Methods("GET")
	
	// 导出日志
	api.HandleFunc("/logs/export", logController.ExportLogs).Methods("GET")

	// ============== WebSocket路由 ==============
	
	// 实时日志流
	router.HandleFunc("/ws/logs/stream", logController.StreamLogs)

	// ============== 健康检查和监控 ==============
	
	// 健康检查
	router.HandleFunc("/health", healthCheckHandler).Methods("GET")
	
	// 服务信息
	router.HandleFunc("/info", serviceInfoHandler).Methods("GET")
	
	// 指标端点
	router.HandleFunc("/metrics", metricsHandler).Methods("GET")

	return router
}

// corsMiddleware CORS中间件
func corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
		w.Header().Set("Access-Control-Allow-Credentials", "true")

		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// loggingMiddleware 日志中间件
func loggingMiddleware(log *logrus.Logger) mux.MiddlewareFunc {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()
			
			// 创建响应记录器
			recorder := &responseRecorder{
				ResponseWriter: w,
				statusCode:     http.StatusOK,
			}
			
			next.ServeHTTP(recorder, r)
			
			duration := time.Since(start)
			
			log.WithFields(logrus.Fields{
				"method":      r.Method,
				"path":        r.URL.Path,
				"status_code": recorder.statusCode,
				"duration_ms": duration.Milliseconds(),
				"client_ip":   getClientIP(r),
				"user_agent":  r.UserAgent(),
			}).Info("HTTP请求")
		})
	}
}

// responseRecorder 响应记录器
type responseRecorder struct {
	http.ResponseWriter
	statusCode int
}

func (r *responseRecorder) WriteHeader(statusCode int) {
	r.statusCode = statusCode
	r.ResponseWriter.WriteHeader(statusCode)
}

// getClientIP 获取客户端IP
func getClientIP(r *http.Request) string {
	// 检查X-Forwarded-For头
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		return strings.Split(xff, ",")[0]
	}
	
	// 检查X-Real-IP头
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}
	
	// 使用RemoteAddr
	ip, _, _ := net.SplitHostPort(r.RemoteAddr)
	return ip
}

// healthCheckHandler 健康检查处理器
func healthCheckHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	
	response := map[string]interface{}{
		"status":    "ok",
		"service":   "log-service",
		"version":   "v1.0.0",
		"timestamp": time.Now().Unix(),
	}
	
	json.NewEncoder(w).Encode(response)
}

// serviceInfoHandler 服务信息处理器
func serviceInfoHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	response := map[string]interface{}{
		"name":        "DevOps Log Service",
		"version":     "v1.0.0",
		"description": "Kubernetes日志收集和分析服务",
		"features": []string{
			"实时日志流",
			"日志搜索和过滤",
			"日志统计分析",
			"多格式导出",
			"WebSocket支持",
		},
		"endpoints": map[string]interface{}{
			"logs": map[string]string{
				"GET /api/v1/logs":            "获取日志",
				"GET /api/v1/logs/statistics": "获取日志统计",
				"GET /api/v1/logs/export":     "导出日志",
			},
			"websocket": map[string]string{
				"WS /ws/logs/stream": "实时日志流",
			},
			"monitoring": map[string]string{
				"GET /health":  "健康检查",
				"GET /info":    "服务信息",
				"GET /metrics": "性能指标",
			},
		},
	}
	
	json.NewEncoder(w).Encode(response)
}

// metricsHandler 指标处理器
func metricsHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	
	// 这里应该收集真实的指标数据
	// 简化实现，返回模拟数据
	response := map[string]interface{}{
		"requests_total":     1000,
		"requests_per_sec":   10.5,
		"avg_response_time":  "150ms",
		"error_rate":         "0.1%",
		"active_connections": 25,
		"memory_usage":       "128MB",
		"cpu_usage":          "15%",
		"uptime":             "24h30m",
	}
	
	json.NewEncoder(w).Encode(response)
}

// 需要导入的包
import (
	"encoding/json"
	"net"
	"strings"
	"time"
)
