package services

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

// EnhancedLogService 增强的日志服务
type EnhancedLogService struct {
	db  *gorm.DB
	log *logrus.Logger
}

// NewEnhancedLogService 创建增强的日志服务
func NewEnhancedLogService(db *gorm.DB, log *logrus.Logger) *EnhancedLogService {
	return &EnhancedLogService{
		db:  db,
		log: log,
	}
}

// LogEntry 日志条目
type LogEntry struct {
	Timestamp   time.Time         `json:"timestamp"`
	Level       string            `json:"level"`
	Message     string            `json:"message"`
	Source      LogSource         `json:"source"`
	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`
	Raw         string            `json:"raw"`
}

// LogSource 日志来源
type LogSource struct {
	Cluster   string `json:"cluster"`
	Namespace string `json:"namespace"`
	Pod       string `json:"pod"`
	Container string `json:"container"`
	Node      string `json:"node"`
}

// LogQuery 日志查询参数
type LogQuery struct {
	ClusterID    uint              `json:"cluster_id"`
	Namespace    string            `json:"namespace"`
	Pod          string            `json:"pod"`
	Container    string            `json:"container"`
	StartTime    *time.Time        `json:"start_time"`
	EndTime      *time.Time        `json:"end_time"`
	Level        string            `json:"level"`
	Keywords     []string          `json:"keywords"`
	Labels       map[string]string `json:"labels"`
	TailLines    int64             `json:"tail_lines"`
	Follow       bool              `json:"follow"`
	Previous     bool              `json:"previous"`
	SinceSeconds *int64            `json:"since_seconds"`
}

// LogStatistics 日志统计
type LogStatistics struct {
	TotalLogs    int64                    `json:"total_logs"`
	ErrorLogs    int64                    `json:"error_logs"`
	WarnLogs     int64                    `json:"warn_logs"`
	InfoLogs     int64                    `json:"info_logs"`
	DebugLogs    int64                    `json:"debug_logs"`
	LogsByLevel  map[string]int64         `json:"logs_by_level"`
	LogsBySource map[string]int64         `json:"logs_by_source"`
	TimeRange    LogTimeRange             `json:"time_range"`
	TopKeywords  []KeywordCount           `json:"top_keywords"`
	LogTrends    []LogTrendPoint          `json:"log_trends"`
}

// LogTimeRange 日志时间范围
type LogTimeRange struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Duration  string    `json:"duration"`
}

// KeywordCount 关键词统计
type KeywordCount struct {
	Keyword string `json:"keyword"`
	Count   int64  `json:"count"`
}

// LogTrendPoint 日志趋势点
type LogTrendPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Count     int64     `json:"count"`
	Level     string    `json:"level"`
}

// LogSearchResult 日志搜索结果
type LogSearchResult struct {
	Logs       []LogEntry     `json:"logs"`
	Total      int64          `json:"total"`
	Statistics LogStatistics  `json:"statistics"`
	Query      LogQuery       `json:"query"`
	HasMore    bool           `json:"has_more"`
	NextCursor string         `json:"next_cursor"`
}

// GetLogs 获取日志
func (s *EnhancedLogService) GetLogs(ctx context.Context, clientset *kubernetes.Clientset, query LogQuery) (*LogSearchResult, error) {
	logs := []LogEntry{}
	
	// 获取Pod列表
	pods, err := s.getPods(ctx, clientset, query)
	if err != nil {
		return nil, fmt.Errorf("获取Pod列表失败: %w", err)
	}

	// 从每个Pod获取日志
	for _, pod := range pods {
		podLogs, err := s.getPodLogs(ctx, clientset, pod, query)
		if err != nil {
			s.log.WithError(err).Warnf("获取Pod %s/%s 日志失败", pod.Namespace, pod.Name)
			continue
		}
		logs = append(logs, podLogs...)
	}

	// 排序日志（按时间戳）
	sort.Slice(logs, func(i, j int) bool {
		return logs[i].Timestamp.Before(logs[j].Timestamp)
	})

	// 应用过滤器
	filteredLogs := s.filterLogs(logs, query)

	// 限制返回数量
	if query.TailLines > 0 && int64(len(filteredLogs)) > query.TailLines {
		start := len(filteredLogs) - int(query.TailLines)
		filteredLogs = filteredLogs[start:]
	}

	// 生成统计信息
	statistics := s.generateStatistics(filteredLogs, query)

	return &LogSearchResult{
		Logs:       filteredLogs,
		Total:      int64(len(filteredLogs)),
		Statistics: statistics,
		Query:      query,
		HasMore:    false, // 简化实现，实际应该检查是否还有更多日志
	}, nil
}

// getPods 获取Pod列表
func (s *EnhancedLogService) getPods(ctx context.Context, clientset *kubernetes.Clientset, query LogQuery) ([]corev1.Pod, error) {
	listOptions := metav1.ListOptions{}
	
	// 构建标签选择器
	if len(query.Labels) > 0 {
		labelSelectors := []string{}
		for key, value := range query.Labels {
			labelSelectors = append(labelSelectors, fmt.Sprintf("%s=%s", key, value))
		}
		listOptions.LabelSelector = strings.Join(labelSelectors, ",")
	}

	if query.Pod != "" {
		// 获取特定Pod
		pod, err := clientset.CoreV1().Pods(query.Namespace).Get(ctx, query.Pod, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		return []corev1.Pod{*pod}, nil
	}

	// 获取所有匹配的Pod
	podList, err := clientset.CoreV1().Pods(query.Namespace).List(ctx, listOptions)
	if err != nil {
		return nil, err
	}

	return podList.Items, nil
}

// getPodLogs 获取单个Pod的日志
func (s *EnhancedLogService) getPodLogs(ctx context.Context, clientset *kubernetes.Clientset, pod corev1.Pod, query LogQuery) ([]LogEntry, error) {
	logOptions := &corev1.PodLogOptions{
		Container:  query.Container,
		Follow:     false, // 对于批量获取，不使用follow
		Previous:   query.Previous,
		Timestamps: true,
	}

	if query.TailLines > 0 {
		logOptions.TailLines = &query.TailLines
	}

	if query.SinceSeconds != nil {
		logOptions.SinceSeconds = query.SinceSeconds
	}

	if query.StartTime != nil {
		startTime := metav1.NewTime(*query.StartTime)
		logOptions.SinceTime = &startTime
	}

	// 获取日志流
	logStream, err := clientset.CoreV1().Pods(pod.Namespace).GetLogs(pod.Name, logOptions).Stream(ctx)
	if err != nil {
		return nil, err
	}
	defer logStream.Close()

	return s.parseLogStream(logStream, pod)
}

// parseLogStream 解析日志流
func (s *EnhancedLogService) parseLogStream(logStream io.ReadCloser, pod corev1.Pod) ([]LogEntry, error) {
	logs := []LogEntry{}
	scanner := bufio.NewScanner(logStream)

	for scanner.Scan() {
		line := scanner.Text()
		logEntry := s.parseLogLine(line, pod)
		if logEntry != nil {
			logs = append(logs, *logEntry)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, err
	}

	return logs, nil
}

// parseLogLine 解析单行日志
func (s *EnhancedLogService) parseLogLine(line string, pod corev1.Pod) *LogEntry {
	// 尝试解析时间戳
	timestamp := time.Now()
	message := line

	// 匹配Kubernetes日志格式: 2023-01-01T00:00:00.000000000Z message
	timestampRegex := regexp.MustCompile(`^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z)\s+(.*)`)
	if matches := timestampRegex.FindStringSubmatch(line); len(matches) == 3 {
		if ts, err := time.Parse(time.RFC3339Nano, matches[1]); err == nil {
			timestamp = ts
			message = matches[2]
		}
	}

	// 检测日志级别
	level := s.detectLogLevel(message)

	return &LogEntry{
		Timestamp: timestamp,
		Level:     level,
		Message:   message,
		Source: LogSource{
			Namespace: pod.Namespace,
			Pod:       pod.Name,
			Node:      pod.Spec.NodeName,
		},
		Labels:      pod.Labels,
		Annotations: pod.Annotations,
		Raw:         line,
	}
}

// detectLogLevel 检测日志级别
func (s *EnhancedLogService) detectLogLevel(message string) string {
	message = strings.ToLower(message)
	
	if strings.Contains(message, "error") || strings.Contains(message, "err") || 
	   strings.Contains(message, "fatal") || strings.Contains(message, "panic") {
		return "ERROR"
	}
	
	if strings.Contains(message, "warn") || strings.Contains(message, "warning") {
		return "WARN"
	}
	
	if strings.Contains(message, "debug") || strings.Contains(message, "trace") {
		return "DEBUG"
	}
	
	return "INFO"
}

// filterLogs 过滤日志
func (s *EnhancedLogService) filterLogs(logs []LogEntry, query LogQuery) []LogEntry {
	filtered := []LogEntry{}

	for _, log := range logs {
		// 时间范围过滤
		if query.StartTime != nil && log.Timestamp.Before(*query.StartTime) {
			continue
		}
		if query.EndTime != nil && log.Timestamp.After(*query.EndTime) {
			continue
		}

		// 日志级别过滤
		if query.Level != "" && log.Level != query.Level {
			continue
		}

		// 关键词过滤
		if len(query.Keywords) > 0 {
			found := false
			for _, keyword := range query.Keywords {
				if strings.Contains(strings.ToLower(log.Message), strings.ToLower(keyword)) {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}

		filtered = append(filtered, log)
	}

	return filtered
}

// generateStatistics 生成统计信息
func (s *EnhancedLogService) generateStatistics(logs []LogEntry, query LogQuery) LogStatistics {
	stats := LogStatistics{
		LogsByLevel:  make(map[string]int64),
		LogsBySource: make(map[string]int64),
		TopKeywords:  []KeywordCount{},
		LogTrends:    []LogTrendPoint{},
	}

	if len(logs) == 0 {
		return stats
	}

	stats.TotalLogs = int64(len(logs))
	stats.TimeRange.StartTime = logs[0].Timestamp
	stats.TimeRange.EndTime = logs[len(logs)-1].Timestamp
	stats.TimeRange.Duration = stats.TimeRange.EndTime.Sub(stats.TimeRange.StartTime).String()

	// 统计各级别日志数量
	for _, log := range logs {
		stats.LogsByLevel[log.Level]++
		
		switch log.Level {
		case "ERROR":
			stats.ErrorLogs++
		case "WARN":
			stats.WarnLogs++
		case "INFO":
			stats.InfoLogs++
		case "DEBUG":
			stats.DebugLogs++
		}

		// 统计来源
		source := fmt.Sprintf("%s/%s", log.Source.Namespace, log.Source.Pod)
		stats.LogsBySource[source]++
	}

	// 生成关键词统计（简化实现）
	keywordMap := make(map[string]int64)
	for _, log := range logs {
		words := strings.Fields(strings.ToLower(log.Message))
		for _, word := range words {
			if len(word) > 3 { // 只统计长度大于3的词
				keywordMap[word]++
			}
		}
	}

	// 转换为排序列表
	for keyword, count := range keywordMap {
		stats.TopKeywords = append(stats.TopKeywords, KeywordCount{
			Keyword: keyword,
			Count:   count,
		})
	}

	// 按计数排序
	sort.Slice(stats.TopKeywords, func(i, j int) bool {
		return stats.TopKeywords[i].Count > stats.TopKeywords[j].Count
	})

	// 只保留前10个
	if len(stats.TopKeywords) > 10 {
		stats.TopKeywords = stats.TopKeywords[:10]
	}

	return stats
}

// ExportLogs 导出日志
func (s *EnhancedLogService) ExportLogs(ctx context.Context, clientset *kubernetes.Clientset, query LogQuery, format string) ([]byte, error) {
	result, err := s.GetLogs(ctx, clientset, query)
	if err != nil {
		return nil, err
	}

	switch format {
	case "json":
		return json.MarshalIndent(result, "", "  ")
	case "csv":
		return s.exportToCSV(result.Logs)
	case "txt":
		return s.exportToText(result.Logs)
	default:
		return nil, fmt.Errorf("不支持的导出格式: %s", format)
	}
}

// exportToCSV 导出为CSV格式
func (s *EnhancedLogService) exportToCSV(logs []LogEntry) ([]byte, error) {
	var builder strings.Builder
	
	// CSV头部
	builder.WriteString("Timestamp,Level,Namespace,Pod,Container,Message\n")
	
	// 数据行
	for _, log := range logs {
		builder.WriteString(fmt.Sprintf("%s,%s,%s,%s,%s,\"%s\"\n",
			log.Timestamp.Format(time.RFC3339),
			log.Level,
			log.Source.Namespace,
			log.Source.Pod,
			log.Source.Container,
			strings.ReplaceAll(log.Message, "\"", "\"\"")))
	}
	
	return []byte(builder.String()), nil
}

// exportToText 导出为文本格式
func (s *EnhancedLogService) exportToText(logs []LogEntry) ([]byte, error) {
	var builder strings.Builder
	
	for _, log := range logs {
		builder.WriteString(fmt.Sprintf("[%s] [%s] [%s/%s] %s\n",
			log.Timestamp.Format("2006-01-02 15:04:05"),
			log.Level,
			log.Source.Namespace,
			log.Source.Pod,
			log.Message))
	}
	
	return []byte(builder.String()), nil
}
