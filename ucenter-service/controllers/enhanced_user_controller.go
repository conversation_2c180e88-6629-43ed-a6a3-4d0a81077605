package controllers

import (
	"net/http"
	"strconv"

	"github.com/devops-microservices/ucenter-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedUserController 增强的用户控制器
type EnhancedUserController struct {
	db                  *gorm.DB
	log                 *logrus.Logger
	enhancedUserService *services.EnhancedUserService
}

// NewEnhancedUserController 创建增强的用户控制器
func NewEnhancedUserController(db *gorm.DB, log *logrus.Logger, enhancedUserService *services.EnhancedUserService) *EnhancedUserController {
	return &EnhancedUserController{
		db:                  db,
		log:                 log,
		enhancedUserService: enhancedUserService,
	}
}

// GetUserMetrics 获取用户指标
// @Tags User Enhanced
// @Summary 获取用户指标
// @Description 获取用户的详细指标，包括用户增长、分布、安全等
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围: 1d|7d|30d" default(7d)
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/users/metrics [get]
func (c *EnhancedUserController) GetUserMetrics(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "7d")

	metrics, err := c.enhancedUserService.GetUserMetrics(ctx.Request.Context(), timeRange)
	if err != nil {
		c.log.WithError(err).Error("获取用户指标失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取用户指标失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": metrics,
	})
}

// GetUserProfile 获取用户档案
// @Tags User Enhanced
// @Summary 获取用户档案
// @Description 获取用户的详细档案信息
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 404 {object} map[string]interface{} "用户未找到"
// @Failure 500 {object} map[string]interface{} "内部错误"
// @Router /api/v1/users/{user_id}/profile [get]
func (c *EnhancedUserController) GetUserProfile(ctx *gin.Context) {
	userIDStr := ctx.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的用户ID",
		})
		return
	}

	profile, err := c.enhancedUserService.GetUserProfile(ctx.Request.Context(), uint(userID))
	if err != nil {
		c.log.WithError(err).Error("获取用户档案失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取用户档案失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": profile,
	})
}

// GetUserGrowthTrend 获取用户增长趋势
// @Tags User Enhanced
// @Summary 获取用户增长趋势
// @Description 获取指定时间范围内的用户增长趋势
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围: 1d|7d|30d" default(7d)
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/users/growth-trend [get]
func (c *EnhancedUserController) GetUserGrowthTrend(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "7d")

	metrics, err := c.enhancedUserService.GetUserMetrics(ctx.Request.Context(), timeRange)
	if err != nil {
		c.log.WithError(err).Error("获取用户增长趋势失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取用户增长趋势失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"growth_trend": metrics.UserGrowthTrend,
			"total_users":  metrics.TotalUsers,
			"new_users":    metrics.NewUsers,
		},
	})
}

// GetUserDistribution 获取用户分布
// @Tags User Enhanced
// @Summary 获取用户分布
// @Description 获取用户的分布统计信息
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/users/distribution [get]
func (c *EnhancedUserController) GetUserDistribution(ctx *gin.Context) {
	metrics, err := c.enhancedUserService.GetUserMetrics(ctx.Request.Context(), "30d")
	if err != nil {
		c.log.WithError(err).Error("获取用户分布失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取用户分布失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"user_distribution": metrics.UserDistribution,
			"role_distribution": metrics.RoleDistribution,
		},
	})
}

// GetLoginStats 获取登录统计
// @Tags User Enhanced
// @Summary 获取登录统计
// @Description 获取用户登录相关的统计信息
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围: 1d|7d|30d" default(7d)
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/users/login-stats [get]
func (c *EnhancedUserController) GetLoginStats(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "7d")

	metrics, err := c.enhancedUserService.GetUserMetrics(ctx.Request.Context(), timeRange)
	if err != nil {
		c.log.WithError(err).Error("获取登录统计失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取登录统计失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": metrics.LoginStats,
	})
}

// GetSecurityMetrics 获取安全指标
// @Tags User Enhanced
// @Summary 获取安全指标
// @Description 获取用户安全相关的指标
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/users/security-metrics [get]
func (c *EnhancedUserController) GetSecurityMetrics(ctx *gin.Context) {
	metrics, err := c.enhancedUserService.GetUserMetrics(ctx.Request.Context(), "30d")
	if err != nil {
		c.log.WithError(err).Error("获取安全指标失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取安全指标失败: " + err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": metrics.SecurityMetrics,
	})
}

// GetUserOverview 获取用户概览
// @Tags User Enhanced
// @Summary 获取用户概览
// @Description 获取用户管理的整体概览信息
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/users/overview [get]
func (c *EnhancedUserController) GetUserOverview(ctx *gin.Context) {
	metrics, err := c.enhancedUserService.GetUserMetrics(ctx.Request.Context(), "7d")
	if err != nil {
		c.log.WithError(err).Error("获取用户概览失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取用户概览失败: " + err.Error(),
		})
		return
	}

	overview := gin.H{
		"summary": gin.H{
			"total_users":   metrics.TotalUsers,
			"active_users":  metrics.ActiveUsers,
			"new_users":     metrics.NewUsers,
			"online_users":  metrics.OnlineUsers,
		},
		"growth_trend":      metrics.UserGrowthTrend,
		"user_distribution": metrics.UserDistribution,
		"role_distribution": metrics.RoleDistribution,
		"login_stats":       metrics.LoginStats,
		"security_metrics":  metrics.SecurityMetrics,
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": overview,
	})
}

// GetUserActivity 获取用户活动
// @Tags User Enhanced
// @Summary 获取用户活动
// @Description 获取指定用户的活动记录
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param limit query int false "返回数量限制" default(20)
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Router /api/v1/users/{user_id}/activity [get]
func (c *EnhancedUserController) GetUserActivity(ctx *gin.Context) {
	userIDStr := ctx.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的用户ID",
		})
		return
	}

	limitStr := ctx.DefaultQuery("limit", "20")
	limit, _ := strconv.Atoi(limitStr)

	profile, err := c.enhancedUserService.GetUserProfile(ctx.Request.Context(), uint(userID))
	if err != nil {
		c.log.WithError(err).Error("获取用户活动失败")
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "获取用户活动失败: " + err.Error(),
		})
		return
	}

	activities := profile.ActivitySummary.RecentActivities
	if len(activities) > limit {
		activities = activities[:limit]
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"activities":       activities,
			"activity_summary": profile.ActivitySummary,
		},
	})
}

// GetUserSessions 获取用户会话
// @Tags User Enhanced
// @Summary 获取用户会话
// @Description 获取指定用户的活跃会话信息
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Router /api/v1/users/{user_id}/sessions [get]
func (c *EnhancedUserController) GetUserSessions(ctx *gin.Context) {
	userIDStr := ctx.Param("user_id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的用户ID",
		})
		return
	}

	// 简化实现，返回模拟会话数据
	sessions := []gin.H{
		{
			"session_id":  "sess_123456",
			"device":      "Chrome on Windows",
			"ip_address":  "*************",
			"location":    "北京, 中国",
			"login_time":  "2024-01-15T09:30:00Z",
			"last_active": "2024-01-15T14:25:00Z",
			"status":      "active",
		},
		{
			"session_id":  "sess_789012",
			"device":      "Safari on iPhone",
			"ip_address":  "*************",
			"location":    "北京, 中国",
			"login_time":  "2024-01-15T08:15:00Z",
			"last_active": "2024-01-15T12:10:00Z",
			"status":      "inactive",
		},
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"user_id":  userID,
			"sessions": sessions,
			"total":    len(sessions),
		},
	})
}

// RevokeUserSession 撤销用户会话
// @Tags User Enhanced
// @Summary 撤销用户会话
// @Description 撤销指定用户的会话
// @Accept json
// @Produce json
// @Param user_id path string true "用户ID"
// @Param session_id path string true "会话ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Router /api/v1/users/{user_id}/sessions/{session_id}/revoke [post]
func (c *EnhancedUserController) RevokeUserSession(ctx *gin.Context) {
	userIDStr := ctx.Param("user_id")
	sessionID := ctx.Param("session_id")

	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "无效的用户ID",
		})
		return
	}

	// 这里应该实现真实的会话撤销逻辑
	c.log.WithFields(logrus.Fields{
		"user_id":    userID,
		"session_id": sessionID,
	}).Info("撤销用户会话")

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"message": "会话已成功撤销",
		},
	})
}
