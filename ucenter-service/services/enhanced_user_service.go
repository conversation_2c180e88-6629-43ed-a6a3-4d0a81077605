package services

import (
	"context"
	"fmt"
	"time"

	"github.com/devops-microservices/ucenter-service/models"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedUserService 增强的用户服务
type EnhancedUserService struct {
	db          *gorm.DB
	log         *logrus.Logger
	userService *UserService
	authService *AuthService
	rbacService *RBACService
}

// NewEnhancedUserService 创建增强的用户服务
func NewEnhancedUserService(db *gorm.DB, log *logrus.Logger, userService *UserService, authService *AuthService, rbacService *RBACService) *EnhancedUserService {
	return &EnhancedUserService{
		db:          db,
		log:         log,
		userService: userService,
		authService: authService,
		rbacService: rbacService,
	}
}

// UserMetrics 用户指标
type UserMetrics struct {
	TotalUsers       int64                 `json:"total_users"`
	ActiveUsers      int64                 `json:"active_users"`
	NewUsers         int64                 `json:"new_users"`
	OnlineUsers      int64                 `json:"online_users"`
	UserGrowthTrend  []UserGrowthPoint     `json:"user_growth_trend"`
	UserDistribution []UserDistribution    `json:"user_distribution"`
	LoginStats       UserLoginStats        `json:"login_stats"`
	SecurityMetrics  UserSecurityMetrics   `json:"security_metrics"`
	RoleDistribution []RoleDistribution    `json:"role_distribution"`
}

// UserGrowthPoint 用户增长点
type UserGrowthPoint struct {
	Date      time.Time `json:"date"`
	NewUsers  int64     `json:"new_users"`
	TotalUsers int64    `json:"total_users"`
}

// UserDistribution 用户分布
type UserDistribution struct {
	Category string `json:"category"`
	Count    int64  `json:"count"`
	Rate     float64 `json:"rate"`
}

// UserLoginStats 用户登录统计
type UserLoginStats struct {
	TotalLogins      int64   `json:"total_logins"`
	SuccessfulLogins int64   `json:"successful_logins"`
	FailedLogins     int64   `json:"failed_logins"`
	SuccessRate      float64 `json:"success_rate"`
	AvgSessionTime   string  `json:"avg_session_time"`
	PeakHours        []int   `json:"peak_hours"`
}

// UserSecurityMetrics 用户安全指标
type UserSecurityMetrics struct {
	PasswordExpired     int64 `json:"password_expired"`
	MFAEnabled          int64 `json:"mfa_enabled"`
	SuspiciousLogins    int64 `json:"suspicious_logins"`
	LockedAccounts      int64 `json:"locked_accounts"`
	SecurityAlerts      int64 `json:"security_alerts"`
	ComplianceScore     float64 `json:"compliance_score"`
}

// RoleDistribution 角色分布
type RoleDistribution struct {
	RoleID   uint   `json:"role_id"`
	RoleName string `json:"role_name"`
	Count    int64  `json:"count"`
	Rate     float64 `json:"rate"`
}

// UserProfile 用户档案
type UserProfile struct {
	UserID          uint                   `json:"user_id"`
	Username        string                 `json:"username"`
	Email           string                 `json:"email"`
	FullName        string                 `json:"full_name"`
	Avatar          string                 `json:"avatar"`
	Department      string                 `json:"department"`
	Position        string                 `json:"position"`
	Phone           string                 `json:"phone"`
	Status          string                 `json:"status"`
	LastLogin       *time.Time             `json:"last_login"`
	CreatedAt       time.Time              `json:"created_at"`
	Roles           []UserRole             `json:"roles"`
	Permissions     []UserPermission       `json:"permissions"`
	Organizations   []UserOrganization     `json:"organizations"`
	SecuritySettings UserSecuritySettings  `json:"security_settings"`
	Preferences     map[string]interface{} `json:"preferences"`
	ActivitySummary UserActivitySummary    `json:"activity_summary"`
}

// UserRole 用户角色
type UserRole struct {
	RoleID      uint      `json:"role_id"`
	RoleName    string    `json:"role_name"`
	Description string    `json:"description"`
	AssignedAt  time.Time `json:"assigned_at"`
	AssignedBy  string    `json:"assigned_by"`
}

// UserPermission 用户权限
type UserPermission struct {
	PermissionID   uint   `json:"permission_id"`
	PermissionName string `json:"permission_name"`
	Resource       string `json:"resource"`
	Action         string `json:"action"`
	Source         string `json:"source"` // role, direct
}

// UserOrganization 用户组织
type UserOrganization struct {
	OrgID       uint   `json:"org_id"`
	OrgName     string `json:"org_name"`
	Role        string `json:"role"`
	Department  string `json:"department"`
	JoinedAt    time.Time `json:"joined_at"`
}

// UserSecuritySettings 用户安全设置
type UserSecuritySettings struct {
	MFAEnabled        bool      `json:"mfa_enabled"`
	PasswordExpiry    *time.Time `json:"password_expiry"`
	LastPasswordChange time.Time `json:"last_password_change"`
	LoginAttempts     int       `json:"login_attempts"`
	AccountLocked     bool      `json:"account_locked"`
	LockExpiry        *time.Time `json:"lock_expiry"`
	TrustedDevices    []string  `json:"trusted_devices"`
}

// UserActivitySummary 用户活动摘要
type UserActivitySummary struct {
	TotalLogins       int64     `json:"total_logins"`
	LastLogin         *time.Time `json:"last_login"`
	SessionCount      int64     `json:"session_count"`
	AvgSessionTime    string    `json:"avg_session_time"`
	RecentActivities  []UserActivity `json:"recent_activities"`
}

// UserActivity 用户活动
type UserActivity struct {
	ActivityID   uint      `json:"activity_id"`
	ActivityType string    `json:"activity_type"`
	Description  string    `json:"description"`
	IPAddress    string    `json:"ip_address"`
	UserAgent    string    `json:"user_agent"`
	Timestamp    time.Time `json:"timestamp"`
	Status       string    `json:"status"`
}

// GetUserMetrics 获取用户指标
func (s *EnhancedUserService) GetUserMetrics(ctx context.Context, timeRange string) (*UserMetrics, error) {
	metrics := &UserMetrics{
		UserGrowthTrend:  []UserGrowthPoint{},
		UserDistribution: []UserDistribution{},
		RoleDistribution: []RoleDistribution{},
	}

	// 计算时间范围
	var startTime time.Time
	switch timeRange {
	case "1d":
		startTime = time.Now().AddDate(0, 0, -1)
	case "7d":
		startTime = time.Now().AddDate(0, 0, -7)
	case "30d":
		startTime = time.Now().AddDate(0, 0, -30)
	default:
		startTime = time.Now().AddDate(0, 0, -7)
	}

	// 获取基础统计
	if err := s.getBasicUserMetrics(ctx, metrics, startTime); err != nil {
		return nil, fmt.Errorf("获取基础用户指标失败: %w", err)
	}

	// 获取用户增长趋势
	if err := s.getUserGrowthTrend(ctx, metrics, startTime); err != nil {
		s.log.WithError(err).Warn("获取用户增长趋势失败")
	}

	// 获取用户分布
	if err := s.getUserDistribution(ctx, metrics); err != nil {
		s.log.WithError(err).Warn("获取用户分布失败")
	}

	// 获取登录统计
	if err := s.getLoginStats(ctx, metrics, startTime); err != nil {
		s.log.WithError(err).Warn("获取登录统计失败")
	}

	// 获取安全指标
	if err := s.getSecurityMetrics(ctx, metrics); err != nil {
		s.log.WithError(err).Warn("获取安全指标失败")
	}

	// 获取角色分布
	if err := s.getRoleDistribution(ctx, metrics); err != nil {
		s.log.WithError(err).Warn("获取角色分布失败")
	}

	return metrics, nil
}

// getBasicUserMetrics 获取基础用户指标
func (s *EnhancedUserService) getBasicUserMetrics(ctx context.Context, metrics *UserMetrics, startTime time.Time) error {
	// 总用户数
	if err := s.db.Model(&models.User{}).Count(&metrics.TotalUsers).Error; err != nil {
		return err
	}

	// 活跃用户数（最近7天有登录）
	activeTime := time.Now().AddDate(0, 0, -7)
	if err := s.db.Model(&models.User{}).Where("last_login > ?", activeTime).Count(&metrics.ActiveUsers).Error; err != nil {
		return err
	}

	// 新用户数
	if err := s.db.Model(&models.User{}).Where("created_at >= ?", startTime).Count(&metrics.NewUsers).Error; err != nil {
		return err
	}

	// 在线用户数（简化实现，使用模拟数据）
	metrics.OnlineUsers = int64(float64(metrics.ActiveUsers) * 0.1) // 假设10%的活跃用户在线

	return nil
}

// getUserGrowthTrend 获取用户增长趋势
func (s *EnhancedUserService) getUserGrowthTrend(ctx context.Context, metrics *UserMetrics, startTime time.Time) error {
	var results []struct {
		Date      time.Time `json:"date"`
		NewUsers  int64     `json:"new_users"`
	}

	query := `
		SELECT 
			DATE(created_at) as date,
			COUNT(*) as new_users
		FROM users 
		WHERE created_at >= ? 
		GROUP BY DATE(created_at) 
		ORDER BY date
	`

	if err := s.db.Raw(query, startTime).Scan(&results).Error; err != nil {
		return err
	}

	var totalUsers int64
	for _, result := range results {
		totalUsers += result.NewUsers
		metrics.UserGrowthTrend = append(metrics.UserGrowthTrend, UserGrowthPoint{
			Date:       result.Date,
			NewUsers:   result.NewUsers,
			TotalUsers: totalUsers,
		})
	}

	return nil
}

// getUserDistribution 获取用户分布
func (s *EnhancedUserService) getUserDistribution(ctx context.Context, metrics *UserMetrics) error {
	// 按状态分布
	var statusResults []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}

	if err := s.db.Model(&models.User{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusResults).Error; err != nil {
		return err
	}

	for _, result := range statusResults {
		rate := float64(result.Count) / float64(metrics.TotalUsers) * 100
		metrics.UserDistribution = append(metrics.UserDistribution, UserDistribution{
			Category: "状态:" + result.Status,
			Count:    result.Count,
			Rate:     rate,
		})
	}

	return nil
}

// getLoginStats 获取登录统计
func (s *EnhancedUserService) getLoginStats(ctx context.Context, metrics *UserMetrics, startTime time.Time) error {
	// 简化实现，返回模拟数据
	metrics.LoginStats = UserLoginStats{
		TotalLogins:      1250,
		SuccessfulLogins: 1180,
		FailedLogins:     70,
		SuccessRate:      94.4,
		AvgSessionTime:   "2h 15m",
		PeakHours:        []int{9, 10, 14, 15, 16},
	}

	return nil
}

// getSecurityMetrics 获取安全指标
func (s *EnhancedUserService) getSecurityMetrics(ctx context.Context, metrics *UserMetrics) error {
	// 简化实现，返回模拟数据
	metrics.SecurityMetrics = UserSecurityMetrics{
		PasswordExpired:  5,
		MFAEnabled:       int64(float64(metrics.TotalUsers) * 0.65), // 65%启用MFA
		SuspiciousLogins: 3,
		LockedAccounts:   2,
		SecurityAlerts:   8,
		ComplianceScore:  87.5,
	}

	return nil
}

// getRoleDistribution 获取角色分布
func (s *EnhancedUserService) getRoleDistribution(ctx context.Context, metrics *UserMetrics) error {
	var results []struct {
		RoleID   uint   `json:"role_id"`
		RoleName string `json:"role_name"`
		Count    int64  `json:"count"`
	}

	query := `
		SELECT 
			r.id as role_id,
			r.name as role_name,
			COUNT(ur.user_id) as count
		FROM roles r
		LEFT JOIN user_roles ur ON r.id = ur.role_id
		GROUP BY r.id, r.name
		ORDER BY count DESC
	`

	if err := s.db.Raw(query).Scan(&results).Error; err != nil {
		return err
	}

	for _, result := range results {
		rate := float64(result.Count) / float64(metrics.TotalUsers) * 100
		metrics.RoleDistribution = append(metrics.RoleDistribution, RoleDistribution{
			RoleID:   result.RoleID,
			RoleName: result.RoleName,
			Count:    result.Count,
			Rate:     rate,
		})
	}

	return nil
}

// GetUserProfile 获取用户档案
func (s *EnhancedUserService) GetUserProfile(ctx context.Context, userID uint) (*UserProfile, error) {
	// 获取用户基本信息
	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return nil, fmt.Errorf("用户不存在: %w", err)
	}

	profile := &UserProfile{
		UserID:    user.ID,
		Username:  user.Username,
		Email:     user.Email,
		FullName:  user.FullName,
		Avatar:    user.Avatar,
		Phone:     user.Phone,
		Status:    user.Status,
		LastLogin: user.LastLogin,
		CreatedAt: user.CreatedAt,
		Roles:     []UserRole{},
		Permissions: []UserPermission{},
		Organizations: []UserOrganization{},
		Preferences: make(map[string]interface{}),
	}

	// 获取用户角色
	if err := s.getUserRoles(ctx, profile); err != nil {
		s.log.WithError(err).Warn("获取用户角色失败")
	}

	// 获取用户权限
	if err := s.getUserPermissions(ctx, profile); err != nil {
		s.log.WithError(err).Warn("获取用户权限失败")
	}

	// 获取安全设置
	s.getUserSecuritySettings(profile)

	// 获取活动摘要
	s.getUserActivitySummary(profile)

	return profile, nil
}

// getUserRoles 获取用户角色
func (s *EnhancedUserService) getUserRoles(ctx context.Context, profile *UserProfile) error {
	var roles []struct {
		RoleID      uint      `json:"role_id"`
		RoleName    string    `json:"role_name"`
		Description string    `json:"description"`
		AssignedAt  time.Time `json:"assigned_at"`
	}

	query := `
		SELECT 
			r.id as role_id,
			r.name as role_name,
			r.description,
			ur.created_at as assigned_at
		FROM roles r
		JOIN user_roles ur ON r.id = ur.role_id
		WHERE ur.user_id = ?
	`

	if err := s.db.Raw(query, profile.UserID).Scan(&roles).Error; err != nil {
		return err
	}

	for _, role := range roles {
		profile.Roles = append(profile.Roles, UserRole{
			RoleID:      role.RoleID,
			RoleName:    role.RoleName,
			Description: role.Description,
			AssignedAt:  role.AssignedAt,
			AssignedBy:  "系统管理员", // 简化实现
		})
	}

	return nil
}

// getUserPermissions 获取用户权限
func (s *EnhancedUserService) getUserPermissions(ctx context.Context, profile *UserProfile) error {
	// 简化实现，返回模拟数据
	profile.Permissions = []UserPermission{
		{PermissionID: 1, PermissionName: "用户管理", Resource: "user", Action: "read", Source: "role"},
		{PermissionID: 2, PermissionName: "项目查看", Resource: "project", Action: "read", Source: "role"},
		{PermissionID: 3, PermissionName: "部署执行", Resource: "deployment", Action: "execute", Source: "direct"},
	}

	return nil
}

// getUserSecuritySettings 获取用户安全设置
func (s *EnhancedUserService) getUserSecuritySettings(profile *UserProfile) {
	// 简化实现，返回模拟数据
	profile.SecuritySettings = UserSecuritySettings{
		MFAEnabled:         true,
		LastPasswordChange: time.Now().AddDate(0, -2, 0),
		LoginAttempts:      0,
		AccountLocked:      false,
		TrustedDevices:     []string{"Chrome on Windows", "Safari on iPhone"},
	}
}

// getUserActivitySummary 获取用户活动摘要
func (s *EnhancedUserService) getUserActivitySummary(profile *UserProfile) {
	// 简化实现，返回模拟数据
	profile.ActivitySummary = UserActivitySummary{
		TotalLogins:    125,
		LastLogin:      profile.LastLogin,
		SessionCount:   8,
		AvgSessionTime: "1h 45m",
		RecentActivities: []UserActivity{
			{ActivityID: 1, ActivityType: "login", Description: "用户登录", IPAddress: "*************", Timestamp: time.Now().Add(-1 * time.Hour), Status: "success"},
			{ActivityID: 2, ActivityType: "view", Description: "查看项目列表", IPAddress: "*************", Timestamp: time.Now().Add(-2 * time.Hour), Status: "success"},
		},
	}
}
