package controllers

import (
	"net/http"
	"strconv"

	"github.com/devops-microservices/workflow-service/middlewares"
	"github.com/devops-microservices/workflow-service/models"
	"github.com/devops-microservices/workflow-service/services"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedWorkflowController 增强的工作流控制器
type EnhancedWorkflowController struct {
	db                      *gorm.DB
	log                     *logrus.Logger
	enhancedWorkflowService *services.EnhancedWorkflowService
}

// NewEnhancedWorkflowController 创建增强的工作流控制器
func NewEnhancedWorkflowController(db *gorm.DB, log *logrus.Logger, enhancedWorkflowService *services.EnhancedWorkflowService) *EnhancedWorkflowController {
	return &EnhancedWorkflowController{
		db:                      db,
		log:                     log,
		enhancedWorkflowService: enhancedWorkflowService,
	}
}

// GetWorkflowMetrics 获取工作流指标
// @Tags Workflow Enhanced
// @Summary 获取工作流指标
// @Description 获取工作流的详细指标，包括完成率、趋势分析等
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围: 1d|7d|30d" default(7d)
// @Success 200 {object} models.DTOResponse "成功响应"
// @Failure 500 {object} models.DTOResponse "内部错误"
// @Router /api/v1/workflows/metrics [get]
func (c *EnhancedWorkflowController) GetWorkflowMetrics(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "7d")

	metrics, err := c.enhancedWorkflowService.GetWorkflowMetrics(ctx.Request.Context(), timeRange)
	if err != nil {
		c.log.WithError(err).Error("获取工作流指标失败")
		middlewares.BusinessErrorResponse(ctx, models.CodeInternalError, "获取工作流指标失败", err.Error())
		return
	}

	middlewares.SuccessResponse(ctx, metrics)
}

// ExecuteWorkflow 执行工作流
// @Tags Workflow Enhanced
// @Summary 执行工作流
// @Description 根据模板执行工作流
// @Accept json
// @Produce json
// @Param template_id path string true "模板ID"
// @Param request body ExecuteWorkflowRequest true "执行参数"
// @Success 200 {object} models.DTOResponse "成功响应"
// @Failure 400 {object} models.DTOResponse "请求错误"
// @Failure 500 {object} models.DTOResponse "内部错误"
// @Router /api/v1/workflows/templates/{template_id}/execute [post]
func (c *EnhancedWorkflowController) ExecuteWorkflow(ctx *gin.Context) {
	templateIDStr := ctx.Param("template_id")
	templateID, err := strconv.ParseUint(templateIDStr, 10, 32)
	if err != nil {
		middlewares.BusinessErrorResponse(ctx, models.CodeBadRequest, "无效的模板ID", err.Error())
		return
	}

	var request ExecuteWorkflowRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		middlewares.BusinessErrorResponse(ctx, models.CodeBadRequest, "请求参数错误", err.Error())
		return
	}

	execution, err := c.enhancedWorkflowService.ExecuteWorkflow(ctx.Request.Context(), uint(templateID), request.Variables)
	if err != nil {
		c.log.WithError(err).Error("执行工作流失败")
		middlewares.BusinessErrorResponse(ctx, models.CodeInternalError, "执行工作流失败", err.Error())
		return
	}

	middlewares.SuccessResponse(ctx, execution)
}

// GetWorkflowTrends 获取工作流趋势
// @Tags Workflow Enhanced
// @Summary 获取工作流趋势
// @Description 获取指定时间范围内的工作流趋势数据
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围: 1d|7d|30d" default(7d)
// @Success 200 {object} models.DTOResponse "成功响应"
// @Router /api/v1/workflows/trends [get]
func (c *EnhancedWorkflowController) GetWorkflowTrends(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "7d")

	metrics, err := c.enhancedWorkflowService.GetWorkflowMetrics(ctx.Request.Context(), timeRange)
	if err != nil {
		c.log.WithError(err).Error("获取工作流趋势失败")
		middlewares.BusinessErrorResponse(ctx, models.CodeInternalError, "获取工作流趋势失败", err.Error())
		return
	}

	response := gin.H{
		"trends":          metrics.WorkflowTrends,
		"completion_rate": metrics.CompletionRate,
		"average_time":    metrics.AverageTime,
	}

	middlewares.SuccessResponse(ctx, response)
}

// GetWorkflowStatusDistribution 获取工作流状态分布
// @Tags Workflow Enhanced
// @Summary 获取工作流状态分布
// @Description 获取工作流的状态分布统计
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围: 1d|7d|30d" default(7d)
// @Success 200 {object} models.DTOResponse "成功响应"
// @Router /api/v1/workflows/status-distribution [get]
func (c *EnhancedWorkflowController) GetWorkflowStatusDistribution(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "7d")

	metrics, err := c.enhancedWorkflowService.GetWorkflowMetrics(ctx.Request.Context(), timeRange)
	if err != nil {
		c.log.WithError(err).Error("获取工作流状态分布失败")
		middlewares.BusinessErrorResponse(ctx, models.CodeInternalError, "获取工作流状态分布失败", err.Error())
		return
	}

	response := gin.H{
		"status_distribution": metrics.StatusDistribution,
		"total_workflows":     metrics.TotalWorkflows,
		"active_workflows":    metrics.ActiveWorkflows,
	}

	middlewares.SuccessResponse(ctx, response)
}

// GetWorkflowCategoryStats 获取工作流分类统计
// @Tags Workflow Enhanced
// @Summary 获取工作流分类统计
// @Description 获取各分类的工作流统计信息
// @Accept json
// @Produce json
// @Param time_range query string false "时间范围: 1d|7d|30d" default(7d)
// @Success 200 {object} models.DTOResponse "成功响应"
// @Router /api/v1/workflows/category-stats [get]
func (c *EnhancedWorkflowController) GetWorkflowCategoryStats(ctx *gin.Context) {
	timeRange := ctx.DefaultQuery("time_range", "7d")

	metrics, err := c.enhancedWorkflowService.GetWorkflowMetrics(ctx.Request.Context(), timeRange)
	if err != nil {
		c.log.WithError(err).Error("获取工作流分类统计失败")
		middlewares.BusinessErrorResponse(ctx, models.CodeInternalError, "获取工作流分类统计失败", err.Error())
		return
	}

	middlewares.SuccessResponse(ctx, metrics.CategoryStats)
}

// GetWorkflowPerformance 获取工作流性能指标
// @Tags Workflow Enhanced
// @Summary 获取工作流性能指标
// @Description 获取工作流的性能相关指标
// @Accept json
// @Produce json
// @Success 200 {object} models.DTOResponse "成功响应"
// @Router /api/v1/workflows/performance [get]
func (c *EnhancedWorkflowController) GetWorkflowPerformance(ctx *gin.Context) {
	metrics, err := c.enhancedWorkflowService.GetWorkflowMetrics(ctx.Request.Context(), "7d")
	if err != nil {
		c.log.WithError(err).Error("获取工作流性能指标失败")
		middlewares.BusinessErrorResponse(ctx, models.CodeInternalError, "获取工作流性能指标失败", err.Error())
		return
	}

	middlewares.SuccessResponse(ctx, metrics.PerformanceMetrics)
}

// GetWorkflowOverview 获取工作流概览
// @Tags Workflow Enhanced
// @Summary 获取工作流概览
// @Description 获取工作流的整体概览信息
// @Accept json
// @Produce json
// @Success 200 {object} models.DTOResponse "成功响应"
// @Router /api/v1/workflows/overview [get]
func (c *EnhancedWorkflowController) GetWorkflowOverview(ctx *gin.Context) {
	metrics, err := c.enhancedWorkflowService.GetWorkflowMetrics(ctx.Request.Context(), "7d")
	if err != nil {
		c.log.WithError(err).Error("获取工作流概览失败")
		middlewares.BusinessErrorResponse(ctx, models.CodeInternalError, "获取工作流概览失败", err.Error())
		return
	}

	overview := gin.H{
		"summary": gin.H{
			"total_workflows":     metrics.TotalWorkflows,
			"active_workflows":    metrics.ActiveWorkflows,
			"completed_workflows": metrics.CompletedWorkflows,
			"pending_workflows":   metrics.PendingWorkflows,
			"completion_rate":     metrics.CompletionRate,
			"average_time":        metrics.AverageTime,
		},
		"trends":              metrics.WorkflowTrends,
		"status_distribution": metrics.StatusDistribution,
		"category_stats":      metrics.CategoryStats,
		"performance":         metrics.PerformanceMetrics,
	}

	middlewares.SuccessResponse(ctx, overview)
}

// GetWorkflowExecution 获取工作流执行状态
// @Tags Workflow Enhanced
// @Summary 获取工作流执行状态
// @Description 获取指定工作流的执行状态
// @Accept json
// @Produce json
// @Param workflow_id path string true "工作流ID"
// @Success 200 {object} models.DTOResponse "成功响应"
// @Failure 400 {object} models.DTOResponse "请求错误"
// @Failure 404 {object} models.DTOResponse "工作流未找到"
// @Router /api/v1/workflows/{workflow_id}/execution [get]
func (c *EnhancedWorkflowController) GetWorkflowExecution(ctx *gin.Context) {
	workflowIDStr := ctx.Param("workflow_id")
	workflowID, err := strconv.ParseUint(workflowIDStr, 10, 32)
	if err != nil {
		middlewares.BusinessErrorResponse(ctx, models.CodeBadRequest, "无效的工作流ID", err.Error())
		return
	}

	// 查询工作流
	var workflow models.Workflow
	if err := c.db.First(&workflow, uint(workflowID)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			middlewares.BusinessErrorResponse(ctx, models.CodeNotFound, "工作流未找到", "")
			return
		}
		c.log.WithError(err).Error("查询工作流失败")
		middlewares.BusinessErrorResponse(ctx, models.CodeInternalError, "查询工作流失败", err.Error())
		return
	}

	// 构造执行状态（简化实现）
	execution := services.WorkflowExecution{
		WorkflowID:  workflow.ID,
		CurrentStep: 2,
		TotalSteps:  3,
		Status:      workflow.Status,
		Progress:    66.7,
		StartTime:   workflow.CreatedAt,
		Variables:   map[string]interface{}{},
		Steps:       []services.WorkflowStepExecution{},
		Logs:        []services.WorkflowLog{},
	}

	middlewares.SuccessResponse(ctx, execution)
}

// StopWorkflow 停止工作流
// @Tags Workflow Enhanced
// @Summary 停止工作流
// @Description 停止正在执行的工作流
// @Accept json
// @Produce json
// @Param workflow_id path string true "工作流ID"
// @Success 200 {object} models.DTOResponse "成功响应"
// @Failure 400 {object} models.DTOResponse "请求错误"
// @Failure 404 {object} models.DTOResponse "工作流未找到"
// @Router /api/v1/workflows/{workflow_id}/stop [post]
func (c *EnhancedWorkflowController) StopWorkflow(ctx *gin.Context) {
	workflowIDStr := ctx.Param("workflow_id")
	workflowID, err := strconv.ParseUint(workflowIDStr, 10, 32)
	if err != nil {
		middlewares.BusinessErrorResponse(ctx, models.CodeBadRequest, "无效的工作流ID", err.Error())
		return
	}

	// 更新工作流状态
	result := c.db.Model(&models.Workflow{}).Where("id = ? AND status IN ?", uint(workflowID), []string{"running", "pending"}).Update("status", "stopped")
	if result.Error != nil {
		c.log.WithError(result.Error).Error("停止工作流失败")
		middlewares.BusinessErrorResponse(ctx, models.CodeInternalError, "停止工作流失败", result.Error.Error())
		return
	}

	if result.RowsAffected == 0 {
		middlewares.BusinessErrorResponse(ctx, models.CodeNotFound, "工作流未找到或无法停止", "")
		return
	}

	middlewares.SuccessResponse(ctx, gin.H{
		"message": "工作流已停止",
	})
}

// ExecuteWorkflowRequest 执行工作流请求
type ExecuteWorkflowRequest struct {
	Variables map[string]interface{} `json:"variables"`
}
